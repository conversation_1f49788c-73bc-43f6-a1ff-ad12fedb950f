window.$config = {
  authServer: "http://localhost:3002",
  authPage: "http://localhost:3002",

  // authServer: "http://************:3002",
  // authPage: "http://************:3002",

  // ============ 🎨 按场景分组的模板库 ============
  // 📷 相机配置说明：
  // 基础配置：
  //   - position: [x, y, z] 相机位置
  //   - target: [x, y, z] 相机目标点
  //   - fov: 视野角度（弧度值）
  //   - minZ: 近裁剪面距离
  //   - panningSensibility: 平移灵敏度
  //
  // 🔧 新增高级配置：
  //   - minDistance/maxDistance: 缩放距离限制
  //   - minPolarAngle/maxPolarAngle: 垂直旋转限制（Beta角度，0-π）
  //   - minAzimuthAngle/maxAzimuthAngle: 水平旋转限制（Alpha角度，可负值）
  //
  // 💡 角度说明：
  //   - PolarAngle（垂直）：0=正上方，π/2=水平，π=正下方
  //   - AzimuthAngle（水平）：0=正前方，π/2=右侧，-π/2=左侧
  //
  // 🔄 滚轮穿透功能：
  //   - wheelPenetration: 是否启用滚轮穿透（true/false）
  //   - wheelPenetrationSpeed: 穿透速度系数（默认0.05）
  // ============================================================

  // ============ 按场景分组的模板库 ============
  templates: {
    1: {
      meshes: {
        buildingLevels: [
          "电气楼五层",
          "NA_LSZSC_QITA04",
          "NA_LSZSC_QITA03",
          "二层外墙",
          "NA_LSZ_YC_MOD10",
        ],
        buildingLevelNames: {
          电气楼五层: "电气楼五层",
          NA_LSZSC_QITA04: "电气楼四层",
          NA_LSZSC_QITA03: "电气楼三层",
          二层外墙: "电气楼二层",
          NA_LSZ_YC_MOD10: "电气楼一层",
        },
        buildingLevelScenes: {
          电气楼五层: "1-5",
          NA_LSZSC_QITA04: "1-4",
          NA_LSZSC_QITA03: "1-3",
          二层外墙: "1-2",
          NA_LSZ_YC_MOD10: "1-1",
        },
        gisDoors: [
          "DOOR_NA_LSZ_GIS_MOD105",
          "DOOR_NA_LSZ_GIS_MOD108",
          "DOOR_NA_LSZ_GIS_MOD114",
          "DOOR_NA_LSZ_GIS_MOD120",
          "DOOR_NA_LSZ_GIS_MOD126",
          "DOOR_NA_LSZ_GIS_MOD72",
          "DOOR_NA_LSZ_GIS_MOD82",
          "DOOR_NA_LSZ_GIS_MOD85",
          "DOOR_NA_LSZ_GIS_MOD95",
        ],
      },
      styles: {
        orangeGradient: {
          backgroundColor:
            "linear-gradient(180deg, rgba(255, 165, 0, 0.9) 0%, rgba(255, 140, 0, 0.8) 50%, rgba(255, 100, 0, 0.9) 100%)",
          borderColor: "rgba(255, 215, 0, 0.8)",
          borderThickness: 2,
          cornerRadius: 8,
          textColor: "#FFFFFF",
          textShadowColor: "rgba(0, 0, 0, 0.5)",
          textShadowBlur: 2,
        },
        blueGradient: {
          backgroundColor:
            "linear-gradient(180deg, rgba(0, 150, 255, 0.9) 0%, rgba(0, 120, 255, 0.8) 50%, rgba(0, 100, 255, 0.9) 100%)",
          borderColor: "rgba(100, 200, 255, 0.8)",
          borderThickness: 2,
          cornerRadius: 6,
          textColor: "#FFFFFF",
          textShadowColor: "rgba(0, 0, 0, 0.5)",
          textShadowBlur: 2,
        },
        transparent: {
          backgroundColor: "rgba(0, 0, 0, 0.7)",
          borderColor: "rgba(255, 255, 255, 0.3)",
          borderThickness: 1,
          cornerRadius: 4,
          textColor: "#FFFFFF",
        },
      },
      interactions: {
        basicHover: {
          enabled: true,
          description: "基础悬停交互 - 青色高亮",
          style: {},
          highlight: {
            enabled: true,
            color: [0, 1, 1],
            intensity: 1.2,
          },
        },
        warningHover: {
          enabled: true,
          description: "警告悬停交互 - 红色高亮",
          style: {},
          highlight: {
            enabled: true,
            color: [0.8, 0, 0],
            intensity: 1.2,
          },
        },
        sceneSwitch: {
          enabled: true,
          callback: "ConfigSceneService.switchToScene",
          parameters: {
            sceneId: "1",
          },
          highlight: {
            enabled: true,
            color: [1, 0.5, 0],
            intensity: 1.5,
            duration: 500,
          },
          style: {
            clickBackgroundColor:
              "linear-gradient(180deg, rgba(255, 100, 0, 1.0) 0%, rgba(255, 69, 0, 0.95) 50%, rgba(255, 50, 0, 1.0) 100%)",
            clickBorderColor: "rgba(255, 215, 0, 1.0)",
            clickScale: 1.2,
            animationDuration: 300,
          },
        },
      },
      positions: {
        buildingPosition: "-2,0,1",
        buildingTarget: "-1.8,0,0",
        floorPosition: "2,2,0",
        floorTarget: "2.2,2.5,0",
        GISPosition: "0.5,0.5,-0.5",
        GISTarget: "0,0.2,0",
        GISDevicePosition: "0,0.1,0",
        GISDeviceTarget: "0,0,0",
        SVGPosition: "-0.5,0.5,-0.5",
        SVGDevicePosition: "0,0.1,0",
        SVGDeviceTarget: "0,0,0",
        SVGTarget: "0,0,0",
      },
      cameras: {
        building: {
          position: [2.76, 2.48, 5.11],
          target: [0.09, 0.38, -0.15],
          fov: 1,
          minZ: 0.01,
          panningSensibility: 9999,
          maxDistance: 50.0,
          minPolarAngle: 0.2,
          maxPolarAngle: 1.3,
        },
        "1-3": {
          position: [-0.24, 4.3, 2.85],
          target: [-0.27, 0.44, -0.23],
          fov: 1,
          minZ: 0.01,
          panningSensibility: 9999,
          maxDistance: 50.0,
          minPolarAngle: 0.2,
          maxPolarAngle: 1.3,
        },
        "3-1": {
          position: [0.55, 1.9, 0.99],
          target: [0.54, 0.54, -0.34],
          fov: 1,
          minZ: 0.01,
          panningSensibility: 9999,
          maxDistance: 50.0,
          minPolarAngle: 0.2,
          maxPolarAngle: 1.3,
        },
        "3-2": {
          position: [-1.01, 2.04, 1.04],
          target: [-1.01, 0.69, -0.2],
          fov: 1,
          minZ: 0.01,
          panningSensibility: 9999,
          maxDistance: 50.0,
          minPolarAngle: 0.2,
          maxPolarAngle: 1.3,
        },
      },
      actions: {
        hoverHighlight: {
          highlight: {
            color: [0, 1, 1],
          },
          description: "悬停测试 - 青色高亮",
        },
        clickRedHighlight: {
          highlight: {
            color: [1, 0, 0],
            duration: 2000,
          },
          description: "单击测试 - 红色高亮",
        },
        doubleClickFocus: {
          highlight: {
            color: [1, 1, 0],
            duration: 1500,
          },
          callback: "CameraService.focusToDevice",
          parameters: {
            deviceName: "{{meshName}}",
            duration: 120,
            paddingFactor: 1,
          },
          description: "双击聚焦 - 黄色高亮 + 相机平滑聚焦到设备",
        },
        blinkingWarning: {
          highlight: {
            color: [1, 0, 0], // 红色
            blinking: {
              enabled: true,
              speed: 2, // 每秒闪2次
              renderOnTop: true, // 新增：在最前端渲染
            },
          },
          description: "单击测试 - 红色闪烁高亮（持久）",
        },
      },
      labels: {
        floorLabel: {
          position: {
            $ref: "templates.1.positions.buildingPosition",
          },
          target: {
            $ref: "templates.1.positions.buildingTarget",
          },
          fontSize: 14,
          group: "设备标签",
          style: {
            $ref: "templates.1.styles.transparent",
          },
          showConnection: true,
          connection: {
            anchorPoint: "left",
            width: 2,
          },
          hover: {
            $ref: "templates.1.interactions.warningHover",
          },
          visible: true,
          description: "楼层标签 - 带连线、hover交互和点击切换",
        },
        roomLabel: {
          fontSize: 14,
          group: "设备标签",
          style: {
            $ref: "templates.1.styles.transparent",
          },
          showConnection: true,
          connection: {
            anchorPoint: "bottom",
            width: 2,
          },
          hover: {
            $ref: "templates.1.interactions.warningHover",
          },
          visible: true,
          description: "房间标签 - 带连线、hover交互和点击切换",
        },
        deviceLabel: {
          fontSize: 11,
          group: "设备标签",
          style: {
            $ref: "templates.1.styles.transparent",
          },
          hover: {
            $ref: "templates.1.interactions.warningHover",
          },
          visible: true,
          description: "设备标签 - 带hover交互和点击切换",
        },
      },
      environments: {
        techStyle: 2,
        basic: 4,
        water: 1,
      },
      resetCallbacks: {
        building: [
          {
            callback: "CameraService.switchCamera",
            parameters: {
              cameraType: "arcRotateCamera",
            },
          },
          {
            callback: "CameraService.moveCamera",
            parameters: {
              position: {
                $ref: "templates.1.cameras.building.position",
              },
              target: {
                $ref: "templates.1.cameras.building.target",
              },
              duration: 120,
            },
          },
          {
            callback: "UIService.showMessage",
            parameters: {
              message: "陆地开关站",
              duration: 2000,
              position: "top-center",
            },
          },
          {
            callback: "AnimationService.toggleMeshAnimation",
            parameters: {
              resetAll: true,
            },
          },
        ],
        "1-3": [
          {
            callback: "CameraService.switchCamera",
            parameters: {
              cameraType: "arcRotateCamera",
            },
          },
          {
            callback: "CameraService.moveCamera",
            parameters: {
              position: {
                $ref: "templates.1.cameras.1-3.position",
              },
              target: {
                $ref: "templates.1.cameras.1-3.target",
              },
              duration: 120,
            },
          },
          {
            callback: "UIService.showMessage",
            parameters: {
              message: "电气楼三层",
              duration: 2000,
              position: "top-center",
            },
          },
          {
            callback: "AnimationService.toggleMeshAnimation",
            parameters: {
              resetAll: true,
            },
          },
        ],
        "3-1": [
          {
            callback: "CameraService.switchCamera",
            parameters: {
              cameraType: "arcRotateCamera",
            },
          },
          {
            callback: "CameraService.moveCamera",
            parameters: {
              position: {
                $ref: "templates.1.cameras.1-3-1.position",
              },
              target: {
                $ref: "templates.1.cameras.1-3-1.target",
              },
              duration: 120,
            },
          },
          {
            callback: "UIService.showMessage",
            parameters: {
              message: "GIS 室",
              duration: 2000,
              position: "top-center",
            },
          },
          {
            callback: "AnimationService.toggleMeshAnimation",
            parameters: {
              resetAll: true,
            },
          },
        ],
        "3-2": [
          {
            callback: "CameraService.switchCamera",
            parameters: {
              cameraType: "arcRotateCamera",
            },
          },
          {
            callback: "CameraService.moveCamera",
            parameters: {
              position: {
                $ref: "templates.1.cameras.1-3-2.position",
              },
              target: {
                $ref: "templates.1.cameras.1-3-2.target",
              },
              duration: 120,
            },
          },
          {
            callback: "UIService.showMessage",
            parameters: {
              message: "SVG 电抗器室",
              duration: 2000,
              position: "top-center",
            },
          },
          {
            callback: "AnimationService.toggleMeshAnimation",
            parameters: {
              resetAll: true,
            },
          },
        ],
      },
    },
  },
  // ============ 🎬 场景配置 ============
  scenes: {
    1: {
      name: "陆地开关站",
      models: ["陆地开关站/陆地开关站楼.glb"],
      scene: "DefaultScene",
      envTemplate: {
        $ref: "templates.1.environments.techStyle",
      },
      lifecycle: {
        onActivated: [
          {
            trigger: "immediate",
            callback: {
              $ref: "templates.1.resetCallbacks.building",
            },
          },
        ],
      },
      camera: {
        $ref: "templates.1.cameras.building",
      },
      staticLabels: [
        {
          $ref: "templates.1.labels.floorLabel",
          $extend: {
            meshNames: ["电气楼五层"],
            customNames: {
              电气楼五层: "电气楼五层",
            },
            click: {
              enabled: true,
              callback: "ConfigSceneService.switchToScene",
              parameters: {
                sceneId: "1-5",
              },
              highlight: {
                enabled: true,
                color: [1, 0.5, 0],
                intensity: 1.5,
                duration: 500,
              },
              description: "点击切换到五层视图",
            },
          },
        },
        {
          $ref: "templates.1.labels.floorLabel",
          $extend: {
            meshNames: ["NA_LSZSC_QITA04"],
            customNames: {
              NA_LSZSC_QITA04: "电气楼四层",
            },
            click: {
              enabled: true,
              callback: "ConfigSceneService.switchToScene",
              parameters: {
                sceneId: "1-4",
              },
              highlight: {
                enabled: true,
                color: [1, 0.5, 0],
                intensity: 1.5,
                duration: 500,
              },
              description: "点击切换到四层视图",
            },
          },
        },
        {
          $ref: "templates.1.labels.floorLabel",
          $extend: {
            meshNames: ["NA_LSZSC_QITA03"],
            customNames: {
              NA_LSZSC_QITA03: "电气楼三层",
            },
            click: {
              enabled: true,
              callback: "ConfigSceneService.switchToScene",
              parameters: {
                sceneId: "1-3",
              },
              highlight: {
                enabled: true,
                color: [1, 0.5, 0],
                intensity: 1.5,
                duration: 500,
              },
              description: "点击切换到三层视图",
            },
          },
        },
        {
          $ref: "templates.1.labels.floorLabel",
          $extend: {
            meshNames: ["二层外墙"],
            customNames: {
              二层外墙: "电气楼二层",
            },
            click: {
              enabled: true,
              callback: "ConfigSceneService.switchToScene",
              parameters: {
                sceneId: "1-2",
              },
              highlight: {
                enabled: true,
                color: [1, 0.5, 0],
                intensity: 1.5,
                duration: 500,
              },
              description: "点击切换到二层视图",
            },
          },
        },
        {
          $ref: "templates.1.labels.floorLabel",
          $extend: {
            meshNames: ["NA_LSZ_YC_MOD10"],
            customNames: {
              NA_LSZ_YC_MOD10: "电气楼一层",
            },
            click: {
              enabled: true,
              callback: "ConfigSceneService.switchToScene",
              parameters: {
                sceneId: "1-1",
              },
              highlight: {
                enabled: true,
                color: [1, 0.5, 0],
                intensity: 1.5,
                duration: 500,
              },
              description: "点击切换到一层视图",
            },
          },
        },
        {
          meshNames: ["NA_LSZ_YC_MOD10"],
          customNames: {
            NA_LSZ_YC_MOD10: "陆地开关站",
          },
          position: {
            $ref: "templates.1.positions.floorPosition",
          },
          fontSize: 16,
          group: "场景控制",
          style: {
            $ref: "templates.1.styles.orangeGradient",
          },
          click: {
            enabled: true,
            callback: "CameraService.moveCamera",
            parameters: {
              position: {
                $ref: "templates.1.cameras.building.position",
              },
              target: {
                $ref: "templates.1.cameras.building.target",
              },
              duration: 120,
            },
            highlight: {
              enabled: true,
              color: [1, 0.5, 0],
              intensity: 1.5,
              duration: 500,
            },
          },
          visible: true,
          description: "场景切换控制标签 - 点击切换到其他场景",
        },
      ],
      actions: [
        {
          meshNames: {
            $ref: "templates.1.meshes.buildingLevels",
          },
          actionType: "hover",
          config: {
            highlight: {
              color: [0, 1, 1],
            },
            description: "悬停测试 - 青色高亮",
          },
        },
        {
          meshNames: {
            $ref: "templates.1.meshes.buildingLevels",
          },
          actionType: "click",
          config: {
            highlight: {
              color: [1, 0, 0],
            },
            description: "单击测试 - 红色持久高亮（点击其他地方取消）",
          },
        },
        {
          meshNames: {
            $ref: "templates.1.meshes.buildingLevels",
          },
          actionType: "doubleClick",
          config: {
            highlight: {
              color: [1, 1, 0],
              // 注意：没有 duration 属性，所以这是持久高亮
            },
            callback: "CameraService.focusToDevice",
            parameters: {
              deviceName: "{{meshName}}",
              duration: 120,
              paddingFactor: 1,
            },
            description: "双击聚焦 - 黄色持久高亮 + 相机平滑聚焦到设备",
          },
        },
        {
          actionType: "rightDoubleClick",
          config: {
            callbacks: {
              $ref: "templates.1.resetCallbacks.building",
            },
          },
        },
        {
          actionType: "hotkey",
          config: {
            key: "r",
            description: "按 R 重置相机和UI",
            callbacks: {
              $ref: "templates.1.resetCallbacks.building",
            },
          },
        },
      ],
    },
    "1-1": {
      name: "陆地开关站-一层",
      models: ["陆地开关站/陆地开关站楼-一层.glb"],
      scene: "DefaultScene",
      envTemplate: {
        $ref: "templates.1.environments.techStyle",
      },
      lifecycle: {
        onActivated: [
          {
            trigger: "immediate",
            callback: {
              $ref: "templates.1.resetCallbacks.1",
            },
          },
        ],
      },
      camera: {
        $ref: "templates.1.cameras.1",
      },

      staticLabels: [
        {
          $ref: "templates.1.labels.floorLabel",
          $extend: {
            meshNames: ["NA_LSZSC_QITA03"],
            customNames: {
              NA_LSZSC_QITA03: "电气楼三层",
            },
            click: {
              enabled: true,
              callback: "CameraService.moveCamera",
              parameters: {
                position: {
                  $ref: "templates.1.cameras.1-3.position",
                },
                target: {
                  $ref: "templates.1.cameras.1-3.target",
                },
                duration: 120,
              },
              highlight: {
                enabled: true,
                color: [1, 0.5, 0],
                intensity: 1.5,
                duration: 500,
              },
            },
          },
        },
        {
          $ref: "templates.1.labels.roomLabel",
          $extend: {
            meshNames: ["GIS室"],
            customNames: {
              GIS室: "GIS室",
            },
            position: {
              $ref: "templates.1.positions.GISPosition",
            },
            target: {
              $ref: "templates.1.positions.GISTarget",
            },
            click: {
              enabled: true,
              callback: "ConfigSceneService.switchToScene",
              parameters: {
                sceneId: "1-3-1",
              },
              highlight: {
                enabled: true,
                color: [1, 0.5, 0],
                intensity: 1.5,
                duration: 500,
              },
            },
          },
        },
        {
          $ref: "templates.1.labels.roomLabel",
          $extend: {
            meshNames: ["SVG电抗器室"],
            customNames: {
              SVG电抗器室: "SVG电抗器室",
            },
            position: {
              $ref: "templates.1.positions.SVGPosition",
            },
            target: {
              $ref: "templates.1.positions.SVGTarget",
            },
            click: {
              enabled: true,
              callback: "ConfigSceneService.switchToScene",
              parameters: {
                sceneId: "1-3-2",
              },
              highlight: {
                enabled: true,
                color: [1, 0.5, 0],
                intensity: 1.5,
                duration: 500,
              },
            },
          },
        },
      ],
      actions: [
        {
          actionType: "rightDoubleClick",
          config: {
            callbacks: {
              $ref: "templates.1.resetCallbacks.1",
            },
          },
        },
        {
          actionType: "hotkey",
          config: {
            key: "r",
            description: "按 R 重置相机和UI",
            callbacks: {
              $ref: "templates.1.resetCallbacks.1",
            },
          },
        },
      ],
    },
    "1-3": {
      name: "陆地开关站-三层",
      models: ["陆地开关站/陆地开关站楼-三层.glb"],
      scene: "DefaultScene",
      envTemplate: {
        $ref: "templates.1.environments.techStyle",
      },
      lifecycle: {
        onActivated: [
          {
            trigger: "immediate",
            callback: {
              $ref: "templates.1.resetCallbacks.3",
            },
          },
        ],
      },
      camera: {
        $ref: "templates.1.cameras.1-3",
      },

      staticLabels: [
        {
          $ref: "templates.1.labels.floorLabel",
          $extend: {
            meshNames: ["NA_LSZSC_QITA03"],
            customNames: {
              NA_LSZSC_QITA03: "电气楼三层",
            },
            click: {
              enabled: true,
              callback: "CameraService.moveCamera",
              parameters: {
                position: {
                  $ref: "templates.1.cameras.1-3.position",
                },
                target: {
                  $ref: "templates.1.cameras.1-3.target",
                },
                duration: 120,
              },
              highlight: {
                enabled: true,
                color: [1, 0.5, 0],
                intensity: 1.5,
                duration: 500,
              },
            },
          },
        },
        {
          $ref: "templates.1.labels.roomLabel",
          $extend: {
            meshNames: ["GIS室"],
            customNames: {
              GIS室: "GIS室",
            },
            position: {
              $ref: "templates.1.positions.GISPosition",
            },
            target: {
              $ref: "templates.1.positions.GISTarget",
            },
            click: {
              enabled: true,
              callback: "ConfigSceneService.switchToScene",
              parameters: {
                sceneId: "1-3-1",
              },
              highlight: {
                enabled: true,
                color: [1, 0.5, 0],
                intensity: 1.5,
                duration: 500,
              },
            },
          },
        },
        {
          $ref: "templates.1.labels.roomLabel",
          $extend: {
            meshNames: ["SVG电抗器室"],
            customNames: {
              SVG电抗器室: "SVG电抗器室",
            },
            position: {
              $ref: "templates.1.positions.SVGPosition",
            },
            target: {
              $ref: "templates.1.positions.SVGTarget",
            },
            click: {
              enabled: true,
              callback: "ConfigSceneService.switchToScene",
              parameters: {
                sceneId: "1-3-2",
              },
              highlight: {
                enabled: true,
                color: [1, 0.5, 0],
                intensity: 1.5,
                duration: 500,
              },
            },
          },
        },
      ],
      actions: [
        {
          actionType: "rightDoubleClick",
          config: {
            callbacks: {
              $ref: "templates.1.resetCallbacks.3",
            },
          },
        },
        {
          actionType: "hotkey",
          config: {
            key: "r",
            description: "按 R 重置相机和UI",
            callbacks: {
              $ref: "templates.1.resetCallbacks.3",
            },
          },
        },
      ],
    },
    "1-3-1": {
      name: "陆地开关站-三层-GIS室",
      models: ["陆地开关站/陆地开关站楼-三层-GIS室.glb"],
      scene: "DefaultScene",
      envTemplate: {
        $ref: "templates.1.environments.techStyle",
      },
      camera: {
        $ref: "templates.1.cameras.1-3-1",
      },
      lifecycle: {
        onActivated: [
          {
            trigger: "immediate",
            callback: {
              $ref: "templates.1.resetCallbacks.3-1",
            },
          },
          {
            trigger: "immediate",
            callback: "DeviceService.setupDataDrivenPolling",
            parameters: {
              dataSource: {
                type: "polling",
                interval: 5000,
                url: "http://127.0.0.1:4523/m1/5128065-4791606-default/base-datas/wf/SBS001/queryPointValue",
                // url: "http://172.16.1.26:19001/base-datas/wf/SBS001/queryPointValue",
                method: "get",
                params: {
                  devDriver: "TS02",
                  driverCode: "TS002",
                  pointType:
                    "yx_16133,yx_16134,yx_16141,yx_16142,yx_16143,yx_16144,yx_16145,yx_16146,yx_16147,yx_16148,yx_16149,yx_16150,yx_12123,yx_12124,yx_12125,yx_12126,yx_8753,yx_8754,yx_8761,yx_8762,yx_8763,yx_8764,yx_8765,yx_8766,yx_8767,yx_8768,yx_8769,yx_8770,yx_13691,yx_13692,yx_13699,yx_13700,yx_13701,yx_13702,yx_13703,yx_13704,yx_13705,yx_13706,yx_13707,yx_13708,yx_10147,yx_10148,yx_10149,yx_10150,yx_10151,yx_10152,yx_13691,yx_13692,yx_13699,yx_13700,yx_13701,yx_13702,yx_13703,yx_13704,yx_13705,yx_13706,yx_13707,yx_13708,yx_11279,yx_11280,yx_11281,yx_11282,yx_11283,yx_11284,yx_11285,yx_11286,yx_10300,yx_10301,yx_10302,yx_10303,yx_10304,yx_10305,yx_16415,yx_16416,yx_16417,yx_16418,yx_16419,yx_16420,yx_16421,yx_16422,yx_16423,yx_16424,yx_9461,yx_9462,yx_9469,yx_9470,yx_9471,yx_9472,yx_9473,yx_9474,yx_9475,yx_9476,yx_9477,yx_9478,yx_13033,yx_13034,yx_13035,yx_13036,yx_14912,yx_14913,yx_14920,yx_14921,yx_14922,yx_14923,yx_14924,yx_14925,yx_14926,yx_14927,yx_14928,yx_14929",
                  stationCode: "SBS001",
                },
                headers: {
                  "Content-Type": "application/json",
                  Type: "SSO",
                  Token: "daa5fde12c25737f2d511d30af26dd68",
                },
                transforms: [
                  {
                    type: "map",
                    input: "value",
                    output: "pointValue",
                    mapping: {
                      0: 0,
                      1: 1,
                    },
                  },
                ],
              },
              mappings: "public/config/mappings/mapping_1-3-1.csv",
              transforms: [
                {
                  type: "map",
                  match: {
                    pointType: "digital",
                    meshType: "分合状态",
                  },
                  input: "pointValue",
                  output: "meshValue",
                  mapping: {
                    0: false,
                    1: true,
                  },
                },
              ],
              callback: "AnimationService.toggleModelVisibility",
              parameters: {
                meshName: "{{meshName}}",
                isVisible: "{{meshValue}}",
              },
            },
            description: "激活数据驱动轮询配置",
          },
        ],
      },
      staticLabels: [
        {
          $ref: "templates.1.labels.roomLabel",
          $extend: {
            meshNames: ["GIS室地"],
            customNames: {
              GIS室地: "GIS室",
            },
            position: {
              $ref: "templates.1.positions.GISPosition",
            },
            target: {
              $ref: "templates.1.positions.GISTarget",
            },
            click: {
              enabled: true,
              callbacks: {
                $ref: "templates.1.resetCallbacks.3-1",
              },
              highlight: {
                enabled: true,
                color: [1, 0.5, 0],
                intensity: 1.5,
                duration: 500,
              },
            },
          },
        },
        {
          $ref: "templates.1.labels.deviceLabel",
          $extend: {
            meshNames: [
              "NA_LSZ_GIS_MOD02",
              "NA_LSZ_GIS_MOD04",
              "NA_LSZ_GIS_MOD05",
              "NA_LSZ_GIS_MOD07",
              "NA_LSZ_GIS_MOD09",
              "NA_LSZ_GIS_MOD10",
              "NA_LSZ_GIS_MOD12",
              "NA_LSZ_GIS_MOD14",
              "NA_LSZ_GIS_MOD16",
            ],
            customNames: {
              NA_LSZ_GIS_MOD02: "220kV 勒广线",
              NA_LSZ_GIS_MOD04: "220kV 勒门丙线",
              NA_LSZ_GIS_MOD05: "220kV 5母",
              NA_LSZ_GIS_MOD07: "220kV 勒门丁线",
              NA_LSZ_GIS_MOD09: "220kV 勒门联线",
              NA_LSZ_GIS_MOD10: "220kV 6母",
              NA_LSZ_GIS_MOD12: "220kV #7B主变",
              NA_LSZ_GIS_MOD14: "220kV 母线",
              NA_LSZ_GIS_MOD16: "220kV #8B主变",
            },
            position: {
              $ref: "templates.1.positions.GISDevicePosition",
            },
            target: {
              $ref: "templates.1.positions.GISDeviceTarget",
            },
            click: {
              enabled: true,
              callback: "CameraService.focusToDevice",
              parameters: {
                deviceName: "{{meshName}}",
                duration: 120,
                paddingFactor: 0.8,
              },
              description: "点击聚焦 - 相机平滑聚焦到设备",
              highlight: {
                enabled: true,
                color: [1, 0.5, 0],
                intensity: 1.5,
                duration: 2000,
              },
            },
          },
        },
      ],
      actions: [
        {
          actionType: "dataDriven",
          dataSource: {
            type: "polling",
            interval: 3000,
            url: "http://127.0.0.1:4523/m1/5128065-4791606-default/base-datas/wf/SBS001/queryPointValue",
            // 接口参考响应数据格式  [{"pointUnit": "A","pointKey": "GIS_STATUS_001","pointDesc": "刀闸A相状态","value": "0"},{"pointUnit": "A","pointKey": "GIS_STATUS_002","pointDesc": "刀闸B相状态","value": "1"}]
            method: "get",
            params: {
              devDriver: "TS01",
              driverCode: "TS001",
              pointType:
                "yx_16141,yx_16142,yx_16143,yx_16144,yx_16145,yx_16146,yx_16147,yx_16148,yx_16149,yx_16150,yx_8753,yx_8754,yx_8761,yx_8762,yx_8763,yx_8764,yx_8765,yx_8766,yx_8767,yx_8768,yx_8769,yx_8770,yx_13691,yx_13692,yx_13699,yx_13700,yx_13701,yx_13702,yx_13703,yx_13704,yx_13705,yx_13706,yx_13707,yx_13708,yx_10147,yx_10148,yx_10149,yx_10150,yx_10151,yx_10152,yk_2989,yk_2990,yk_2991,yk_2992,yk_2993,yk_2994,yx_11279,yx_11280,yx_11281,yx_11282,yx_11283,yx_11284,yx_11285,yx_11286,yx_10300,yx_10301,yx_10302,yx_10303,yx_10304,yx_10305,yx_16415,yx_16416,yx_16417,yx_16418,yx_16419,yx_16420,yx_16421,yx_16422,yx_16423,yx_16424,yx_9461,yx_9462,yx_9469,yx_9470,yx_9471,yx_9472,yx_9473,yx_9474,yx_9475,yx_9476,yx_9477,yx_9478,yx_14912,yx_14913,yx_14920,yx_14921,yx_14922,yx_14923,yx_14924,yx_14925,yx_14926,yx_14927,yx_14928,yx_14929",
              stationCode: "SBS001",
            },
            headers: {
              "Content-Type": "application/json",
              Type: "SSO",
              Token: "daa5fde12c25737f2d511d30af26dd68",
            },
            transforms: [
              {
                type: "map",
                input: "value",
                output: "pointValue",
                mapping: {
                  0: 0,
                  1: 1,
                },
              },
            ],
          },
          mappings: "public/config/mappings/mapping_1-3-1.csv",
          driveData: {
            transforms: [
              {
                type: "map",
                match: {
                  pointType: "digital",
                  meshType: "分合状态",
                },
                input: "pointValue",
                output: "meshValue",
                mapping: {
                  0: -1,
                  1: 1,
                },
              },
              {
                type: "range",
                match: {
                  pointType: "analog",
                  meshType: "温度",
                },
                input: "pointValue",
                output: "meshValue",
                mapping: {
                  from: [0, 150],
                  to: [0, 1],
                },
              },
            ],
          },
          config: {
            callback: "AnimationService.playMeshAnimation",
            parameters: {
              meshName: "{{meshName}}",
              speed: "{{meshValue}}",
            },
          },
        },
        {
          meshNames: {
            $ref: "templates.1.meshes.gisDoors",
          },
          actionType: "hover",
          config: {
            $ref: "templates.1.actions.hoverHighlight",
          },
        },
        {
          meshNames: {
            $ref: "templates.1.meshes.gisDoors",
          },
          actionType: "doubleClick",
          config: {
            callback: "AnimationService.toggleMeshAnimation",
            parameters: {
              meshName: "{{meshName}}",
              speed: 10.0,
            },
            description: "双击播放与网格相关的第一个动画组",
          },
        },
        {
          actionType: "rightDoubleClick",
          config: {
            callbacks: {
              $ref: "templates.1.resetCallbacks.3-1",
            },
          },
        },
        {
          actionType: "hotkey",
          config: {
            key: "r",
            description: "按 R 重置相机和UI",
            callbacks: {
              $ref: "templates.1.resetCallbacks.3-1",
            },
          },
        },
        {
          actionType: "hotkey",
          config: {
            key: "m",
            description: "按 M 显示/隐藏数据驱动监控面板",
            callback: "UIService.showDataDrivenMonitor",
          },
        },
      ],
    },
    "1-3-2": {
      name: "陆地开关站-三层-SVG电抗器室",
      models: ["陆地开关站/陆地开关站楼-三层-SVG电抗器室.glb"],
      scene: "DefaultScene",
      envTemplate: {
        $ref: "templates.1.environments.techStyle",
      },
      camera: {
        $ref: "templates.1.cameras.1-3-2",
      },
      lifecycle: {},
      staticLabels: [
        {
          $ref: "templates.1.labels.roomLabel",
          $extend: {
            meshNames: ["SVG电抗器室地"],
            customNames: {
              SVG电抗器室地: "SVG电抗器室",
            },
            position: {
              $ref: "templates.1.positions.SVGPosition",
            },
            target: {
              $ref: "templates.1.positions.SVGTarget",
            },
            click: {
              enabled: true,
              callbacks: {
                $ref: "templates.1.resetCallbacks.3-2",
              },
              highlight: {
                enabled: true,
                color: [1, 0.5, 0],
                intensity: 1.5,
                duration: 500,
              },
            },
          },
        },
        {
          $ref: "templates.1.labels.deviceLabel",
          $extend: {
            meshNames: [
              "NA_LSZ_GIS_MOD02",
              "NA_LSZ_GIS_MOD04",
              "NA_LSZ_GIS_MOD05",
              "NA_LSZ_GIS_MOD07",
              "NA_LSZ_GIS_MOD09",
              "NA_LSZ_GIS_MOD10",
              "NA_LSZ_GIS_MOD12",
              "NA_LSZ_GIS_MOD14",
              "NA_LSZ_GIS_MOD16",
            ],
            customNames: {
              NA_LSZ_GIS_MOD02: "220kV 勒广线",
              NA_LSZ_GIS_MOD04: "220kV 勒门丙线",
              NA_LSZ_GIS_MOD05: "220kV 5母",
              NA_LSZ_GIS_MOD07: "220kV 勒门丁线",
              NA_LSZ_GIS_MOD09: "220kV 勒门联线",
              NA_LSZ_GIS_MOD10: "220kV 6母",
              NA_LSZ_GIS_MOD12: "220kV #7B主变",
              NA_LSZ_GIS_MOD14: "220kV 母线",
              NA_LSZ_GIS_MOD16: "220kV #8B主变",
            },
            position: {
              $ref: "templates.1.positions.GISDevicePosition",
            },
            target: {
              $ref: "templates.1.positions.GISDeviceTarget",
            },
            click: {
              enabled: true,
              callback: "CameraService.focusToDevice",
              parameters: {
                deviceName: "{{meshName}}",
                duration: 120,
                paddingFactor: 0.8,
              },
              description: "点击聚焦 - 相机平滑聚焦到设备",
              highlight: {
                enabled: true,
                color: [1, 0.5, 0],
                intensity: 1.5,
                duration: 2000,
              },
            },
          },
        },
      ],
      actions: [
        {
          actionType: "rightDoubleClick",
          config: {
            callbacks: {
              $ref: "templates.1.resetCallbacks.3-2",
            },
          },
        },
        {
          actionType: "hotkey",
          config: {
            key: "r",
            description: "按 R 重置相机和UI",
            callbacks: {
              $ref: "templates.1.resetCallbacks.3-2",
            },
          },
        },
        {
          actionType: "hotkey",
          config: {
            key: "m",
            description: "按 M 显示/隐藏数据驱动监控面板",
            callback: "UIService.showDataDrivenMonitor",
          },
        },
      ],
    },
  },
};
