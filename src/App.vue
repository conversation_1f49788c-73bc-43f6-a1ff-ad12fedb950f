<script setup lang="ts">
import { ref, onMounted, watch, provide } from "vue";
import LogicEditor from "./components/LogicEditor.vue";
import NodeLibrary from "./components/NodeLibrary.vue";
import PropertiesPanel from "./components/PropertiesPanel.vue";
import SceneManager from "./components/scene/SceneManager.vue";
import { sceneService } from "./services/SceneService";
import { errorService } from "./services/ErrorService";
import { compileMultipleGraphs } from "./compiler/graph-compiler";
import type {
  AppState,
  NodeSelectedEvent,
  ConfigGeneratedEvent,
  NodeTypeChangedEvent,
  SceneEvent
} from "./types";

// 应用状态
const appState = ref<AppState>({
  selectedNode: null,
  currentSceneId: null,
  generatedConfig: "",
  isLibraryCollapsed: false,
  isPropertiesCollapsed: false,
});

// 逻辑编辑器实例
const logicEditor = ref<InstanceType<typeof LogicEditor> | null>(null);
const sceneManagerRef = ref<any>(null);

// 将场景ID注入到子组件
provide("currentSceneId", appState);

// 初始化加载
onMounted(async () => {
  try {
    // 设置当前场景ID
    appState.value.currentSceneId = sceneService.getCurrentSceneId();

    // 添加错误处理器
    errorService.addErrorHandler(handleApplicationError);
  } catch (error) {
    errorService.handleError(error as Error, 'App.onMounted');
  }
});

// 当场景ID变化时，保存当前场景数据并加载新场景
watch(() => appState.value.currentSceneId, async (newId, oldId) => {
  try {
    // 如果有旧的场景ID，先保存当前编辑器的内容
    if (oldId && logicEditor.value) {
      const graphData = logicEditor.value.saveGraphData();
      sceneService.updateGraphData(oldId, graphData);
    }

    // 如果有新的场景ID，加载该场景的内容
    if (newId && logicEditor.value) {
      const scene = sceneService.getCurrentScene();
      if (scene && scene.graphData) {
        logicEditor.value.loadGraphData(scene.graphData);
      } else {
        // 如果是新场景没有图数据，清空编辑器
        logicEditor.value.clearGraph();
      }
    }
  } catch (error) {
    errorService.handleError(error as Error, 'App.watchCurrentSceneId');
  }
});

// 应用错误处理器
function handleApplicationError(error: any) {
  // 在这里可以显示用户友好的错误提示
  console.error('应用错误:', error);
  // 可以添加 toast 通知或其他用户界面反馈
}

// 选中节点处理
function handleNodeSelected(node: any) {
  try {
    appState.value.selectedNode = node;
  } catch (error) {
    errorService.handleError(error as Error, 'App.handleNodeSelected');
  }
}

// 节点数据更新处理
function handleNodeDataUpdated(event: { nodeId: string; data: any }) {
  try {
    // 如果逻辑编辑器实例存在，更新节点数据
    if (logicEditor.value) {
      logicEditor.value.updateNodeData(event.nodeId, event.data);
    }

    // 保存当前场景
    saveCurrentScene();
  } catch (error) {
    errorService.handleError(error as Error, 'App.handleNodeDataUpdated');
  }
}

// 处理节点类型变更
function handleNodeTypeChanged(event: NodeTypeChangedEvent) {
  try {
    console.log(
      `节点类型变更: ${event.nodeId} 从 ${event.oldType} 变更为 ${event.newType}`
    );

    // 如果逻辑编辑器实例存在，直接调用其方法
    if (logicEditor.value) {
      logicEditor.value.changeNodeType(
        event.nodeId,
        event.oldType,
        event.newType,
        event.newData
      );
    }

    // 保存当前场景
    saveCurrentScene();
  } catch (error) {
    errorService.handleError(error as Error, 'App.handleNodeTypeChanged');
  }
}

// 保存当前场景数据
function saveCurrentScene() {
  try {
    if (appState.value.currentSceneId && logicEditor.value) {
      const graphData = logicEditor.value.saveGraphData();
      sceneService.updateGraphData(appState.value.currentSceneId, graphData);
      sceneService.saveToLocalStorage();
    }
  } catch (error) {
    errorService.handleError(error as Error, 'App.saveCurrentScene');
  }
}

// 处理场景选择
function handleSceneSelected(sceneId: string) {
  try {
    appState.value.currentSceneId = sceneId;
    console.log(`已选择场景: ${sceneId}`);
  } catch (error) {
    errorService.handleError(error as Error, 'App.handleSceneSelected');
  }
}

// 处理场景创建
function handleSceneCreated(event: SceneEvent) {
  try {
    appState.value.currentSceneId = event.sceneId;
    console.log(`已创建场景: ${event.sceneId}`);

    // 在新场景创建后，需要让逻辑编辑器知道创建了新场景
    setTimeout(() => {
      if (logicEditor.value) {
        const customEvent = new CustomEvent("scene-created", {
          detail: { sceneId: event.sceneId },
        });
        document.dispatchEvent(customEvent);
      }
    }, 100);
  } catch (error) {
    errorService.handleError(error as Error, 'App.handleSceneCreated');
  }
}



// 处理配置生成
function handleConfigGenerated(event: ConfigGeneratedEvent) {
  try {
    saveCurrentScene();

    // 收集所有场景的图数据
    const allGraphsData: Record<string, any> = {};
    const scenes = sceneService.getAllScenes();

    scenes.forEach((scene) => {
      if (scene.graphData) {
        allGraphsData[scene.configId || scene.id] = scene.graphData;
      }
    });

    // 编译多场景配置
    const compiledConfig = compileMultipleGraphs(
      allGraphsData,
      appState.value.currentSceneId || undefined
    );

    // 格式化为可读的JSON字符串
    appState.value.generatedConfig = JSON.stringify(compiledConfig, null, 2);
  } catch (error) {
    errorService.handleError(error as Error, 'App.handleConfigGenerated');
  }
}

// 切换节点库面板显示状态
function toggleLibraryPanel() {
  try {
    appState.value.isLibraryCollapsed = !appState.value.isLibraryCollapsed;
  } catch (error) {
    errorService.handleError(error as Error, 'App.toggleLibraryPanel');
  }
}

// 切换属性面板显示状态
function togglePropertiesPanel() {
  try {
    appState.value.isPropertiesCollapsed = !appState.value.isPropertiesCollapsed;
  } catch (error) {
    errorService.handleError(error as Error, 'App.togglePropertiesPanel');
  }
}

// 测试连接功能
function testConnection() {
  try {
    if (logicEditorRef.value && logicEditorRef.value.testConnection) {
      logicEditorRef.value.testConnection()
    }
  } catch (error) {
    errorService.handleError(error as Error, 'App.testConnection')
  }
}
</script>

<template>
  <div class="app-container">
    <!-- 顶部工具栏 -->
    <div class="app-toolbar">
      <h1>DDD-Flow 可视化场景编辑器</h1>
      <div class="toolbar-actions">
        <button @click="testConnection" class="toolbar-btn test-btn">
          🧪 测试连接
        </button>
        <button @click="toggleLibraryPanel" class="toolbar-btn">
          {{ appState.isLibraryCollapsed ? "显示节点库" : "隐藏节点库" }}
        </button>
        <button @click="togglePropertiesPanel" class="toolbar-btn">
          {{ appState.isPropertiesCollapsed ? "显示属性面板" : "隐藏属性面板" }}
        </button>
      </div>
    </div>

    <div class="app-content">
      <!-- 左侧面板 - 节点库 -->
      <div class="left-panel" :class="{ collapsed: appState.isLibraryCollapsed }">
        <button class="collapse-btn" @click="toggleLibraryPanel">
          {{ appState.isLibraryCollapsed ? ">" : "<" }}
        </button>

        <div v-if="!appState.isLibraryCollapsed" class="panel-content">
          <!-- 场景管理器组件 -->
          <SceneManager
            ref="sceneManagerRef"
            :currentSceneId="appState.currentSceneId"
            @scene-selected="handleSceneSelected"
            @scene-created="handleSceneCreated"
            @scene-deleted="saveCurrentScene"
            @scene-renamed="saveCurrentScene"
          />

          <!-- 节点库组件 -->
          <NodeLibrary />
        </div>
      </div>

      <!-- 中间编辑区 -->
      <div class="main-content">
        <!-- 逻辑编辑器组件 -->
        <LogicEditor
          ref="logicEditor"
          @node-selected="handleNodeSelected"
          @config-generated="handleConfigGenerated"
          @node-type-changed="handleNodeTypeChanged"
        />

        <!-- 编译结果显示区域 -->
        <div v-if="appState.generatedConfig" class="config-output">
          <div class="config-header">
            <h3>生成的配置</h3>
            <button @click="appState.generatedConfig = ''" class="close-btn">
              关闭
            </button>
          </div>
          <pre>{{ appState.generatedConfig }}</pre>
        </div>
      </div>

      <!-- 右侧面板 - 属性编辑器 -->
      <div class="right-panel" :class="{ collapsed: appState.isPropertiesCollapsed }">
        <button class="collapse-btn" @click="togglePropertiesPanel">
          {{ appState.isPropertiesCollapsed ? "<" : ">" }}
        </button>

        <div v-if="!appState.isPropertiesCollapsed" class="panel-content">
          <!-- 属性面板组件 -->
          <PropertiesPanel
            :selectedNode="appState.selectedNode"
            @update-node="handleNodeDataUpdated"
            @change-node-type="handleNodeTypeChanged"
          />

          <!-- 小地图区域 -->
          <div class="minimap-wrapper">
            <h3>小地图</h3>
            <div id="minimap-container"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.app-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: #1e1e1e;
  color: #e0e0e0;
}

.app-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background-color: #252525;
  border-bottom: 1px solid #333;
}

.app-toolbar h1 {
  margin: 0;
  font-size: 20px;
}

.toolbar-actions {
  display: flex;
  gap: 10px;
}

.toolbar-btn {
  padding: 8px 12px;
  background-color: #333;
  border: none;
  border-radius: 4px;
  color: #e0e0e0;
  cursor: pointer;
}

.toolbar-btn:hover {
  background-color: #4a4a4a;
}

.test-btn {
  background-color: #ff6b35 !important;
  color: white;
}

.test-btn:hover {
  background-color: #e55a2b !important;
}

.app-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.left-panel,
.right-panel {
  position: relative;
  width: 300px;
  background-color: #252525;
  transition: width 0.3s;
  overflow: hidden;
}

.left-panel.collapsed,
.right-panel.collapsed {
  width: 30px;
}

.panel-content {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.collapse-btn {
  position: absolute;
  top: 50%;
  right: 0;
  width: 20px;
  height: 60px;
  background-color: #333;
  border: none;
  color: #e0e0e0;
  cursor: pointer;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px 0 0 3px;
}

.right-panel .collapse-btn {
  left: 0;
  right: auto;
  border-radius: 0 3px 3px 0;
}

.main-content {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.config-output {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 300px;
  background-color: #252525;
  border-top: 1px solid #333;
  overflow: auto;
  z-index: 100;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #333;
}

.config-header h3 {
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: #e0e0e0;
  cursor: pointer;
}

.config-output pre {
  margin: 0;
  padding: 10px;
  white-space: pre-wrap;
  font-family: monospace;
}

.minimap-wrapper {
  padding: 10px;
  border-top: 1px solid #444;
}

.minimap-wrapper h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
}

#minimap-container {
  width: 100%;
  height: 150px;
  background-color: #1e1e1e;
  border: 1px solid #444;
}
</style>
