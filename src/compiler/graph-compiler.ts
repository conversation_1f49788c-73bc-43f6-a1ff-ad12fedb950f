import { Cell } from "@antv/x6";

// 定义配置输出接口
export interface CompiledConfig {
  scenes?: { [key: string]: SceneConfig };
  templates?: { [key: string]: any };
  defaultScene?: string;
  authServer?: string;
  authPage?: string;
}

// 场景配置接口
export interface SceneConfig {
  id?: string;
  name?: string;
  models?: string[];
  actions?: ActionConfig[];
  scene?: string;
  envTemplate?: any;
  camera?: any;
  staticLabels?: any[];
  lifecycle?: {
    onActivated?: any[];
  };
  [key: string]: any;
}

// 动作配置接口
export interface ActionConfig {
  meshNames?: string[] | { $ref: string };
  actionType?: string;
  config?: {
    highlight?: {
      color?: number[];
      duration?: number;
      enabled?: boolean;
      intensity?: number;
    };
    callback?: string;
    parameters?: any;
    callbacks?: any[];
    description?: string;
  };
  [key: string]: any;
}

/**
 * 编译多个场景节点图为完整配置
 * @param graphsData 多个场景的节点图数据
 * @param defaultSceneId 默认场景ID
 * @returns 完整配置对象
 */
export function compileMultipleGraphs(
  graphsData: { [sceneId: string]: any },
  defaultSceneId?: string
): CompiledConfig {
  const config: CompiledConfig = {
    scenes: {},
    templates: {
      1: {
        cameras: {},
        styles: {},
        actions: {},
        labels: {},
        environments: {},
        meshes: {},
        positions: {},
        interactions: {},
        resetCallbacks: {},
      },
    },
    defaultScene: defaultSceneId,
    authServer: "http://localhost:3002",
    authPage: "http://localhost:3002",
  };

  // 编译每个场景
  for (const [sceneId, graphData] of Object.entries(graphsData)) {
    const sceneConfig = compileGraph(graphData);
    if (sceneConfig) {
      config.scenes![sceneId] = sceneConfig;
    }
  }

  return config;
}

/**
 * 编译单个场景的节点图为场景配置
 * @param graphData 节点图数据
 * @returns 场景配置对象
 */
export function compileGraph(graphData: any): SceneConfig {
  if (!graphData || !graphData.cells) {
    return {};
  }

  const cells = graphData.cells;
  const nodes = cells.filter((cell: Cell.Properties) => cell.shape !== "edge");
  const edges = cells.filter((cell: Cell.Properties) => cell.shape === "edge");

  // 查找场景配置节点
  const sceneConfigNode = nodes.find(
    (node: Cell.Properties) =>
      node.data && node.data.nodeType === "scene-config"
  );

  // 基础场景配置
  const sceneConfig: SceneConfig = {
    name: sceneConfigNode?.data?.sceneName || "未命名场景",
    models: sceneConfigNode?.data?.models || [],
    scene: sceneConfigNode?.data?.scene || "DefaultScene",
    envTemplate: {
      $ref: "templates.1.environments.techStyle",
    },
    camera: {
      $ref: "templates.1.cameras.building",
    },
    staticLabels: [],
    actions: [],
  };

  // 查找所有生命周期节点
  const lifecycleNodes = nodes.filter(
    (node: Cell.Properties) => node.data && node.data.nodeType === "lifecycle"
  );

  // 如果有生命周期节点，添加lifecycle配置
  if (lifecycleNodes.length > 0) {
    sceneConfig.lifecycle = {};

    // 按生命周期类型分组
    const lifecycleNodesByType: Record<string, Cell.Properties[]> = {};
    lifecycleNodes.forEach((node) => {
      const lifecycleType = node.data.lifecycleType;
      if (!lifecycleNodesByType[lifecycleType]) {
        lifecycleNodesByType[lifecycleType] = [];
      }
      lifecycleNodesByType[lifecycleType].push(node);
    });

    // 处理每种生命周期类型
    for (const lifecycleType in lifecycleNodesByType) {
      const nodesOfType = lifecycleNodesByType[lifecycleType];

      // 为每个生命周期类型创建一个配置数组
      const lifecycleEvents: any[] = [];

      // 处理每个生命周期节点
      nodesOfType.forEach((node) => {
        // 查找生命周期节点连接的后续节点
        const nextNodes = findNextNodes(node.id, "out-exec", nodes, edges);
        if (nextNodes.length > 0) {
          // 处理链式节点，获取完整回调配置
          const config = processChainingNodes(nextNodes[0], nodes, edges);

          if (config) {
            // 创建生命周期事件配置
            const lifecycleEvent: any = {
              trigger: node.data.trigger || "immediate",
              callback: config.callback ? config : config.callbacks || config,
            };

            // 如果有描述，添加到配置中
            if (node.data.description) {
              lifecycleEvent.description = node.data.description;
            }

            lifecycleEvents.push(lifecycleEvent);
          }
        }
      });

      // 将该类型的生命周期事件添加到配置中
      if (lifecycleEvents.length > 0) {
        sceneConfig.lifecycle[lifecycleType] = lifecycleEvents;
      }
    }
  }

  // 查找所有事件节点（起点节点）
  const eventNodes = nodes.filter(
    (node: Cell.Properties) => node.data && node.data.nodeType === "event"
  );

  // 处理每个事件节点
  for (const eventNode of eventNodes) {
    // 获取事件节点数据
    const eventData = eventNode.data;
    if (!eventData) continue;

    // 创建符合config.js格式的动作配置
    const actionConfig: ActionConfig = {
      meshNames: eventData.meshNames || ["DEFAULT_MESH"],
      actionType: eventData.actionType || "doubleClick",
      config: {
        description:
          eventData.description || `${eventData.actionType || "事件"}动作`,
      },
    };

    // 处理事件的下游节点
    const nextNodes = findNextNodes(eventNode.id, "out-exec", nodes, edges);
    if (nextNodes.length > 0) {
      // 递归处理下游节点
      const resultConfig = processChainingNodes(nextNodes[0], nodes, edges);

      // 合并到当前动作配置
      if (resultConfig && actionConfig.config) {
        Object.assign(actionConfig.config, resultConfig);
      }
    }

    // 添加到场景动作配置列表
    if (sceneConfig.actions) {
      sceneConfig.actions.push(actionConfig);
    }
  }

  return sceneConfig;
}

/**
 * 处理链式节点并组装config.js格式的配置
 */
function processChainingNodes(
  node: Cell.Properties,
  allNodes: Cell.Properties[],
  allEdges: Cell.Properties[],
  processedNodes: Set<string> = new Set()
): any {
  // 防止循环引用
  if (processedNodes.has(node.id)) {
    return null;
  }
  processedNodes.add(node.id);

  // 获取节点数据
  const nodeData = node.data;
  if (!nodeData) return null;

  let config: any = {};

  switch (nodeData.nodeType) {
    case "action-highlight":
      // 处理高亮配置
      config.highlight = {
        color: nodeData.color || [1, 1, 0],
        duration: nodeData.duration || 0,
        enabled: true,
        intensity: nodeData.intensity || 1.2,
      };
      break;

    case "action-callback":
      // 处理回调配置
      config.callback = nodeData.callback || "";
      config.parameters = nodeData.parameters || {};
      break;

    case "data-source":
      // 处理数据源配置
      config.dataSource = nodeData.dataSource || {
        type: "polling",
        interval: 5000,
      };

      // 如果有描述，添加到配置中
      if (nodeData.description) {
        config.description = nodeData.description;
      }
      break;

    case "data-transform":
      // 处理数据转换配置
      if (!config.dataSource) {
        config.dataSource = {};
      }

      if (!config.dataSource.transforms) {
        config.dataSource.transforms = [];
      }

      // 添加转换配置
      const transform = {
        type: nodeData.transformType || "map",
        input: nodeData.transform?.input || "value",
        output: nodeData.transform?.output || "transformedValue",
      };

      // 根据转换类型添加特定配置
      if (nodeData.transformType === "map" && nodeData.transform?.mapping) {
        transform.mapping = nodeData.transform.mapping;
      } else if (
        nodeData.transformType === "range" &&
        nodeData.transform?.from &&
        nodeData.transform?.to
      ) {
        transform.from = nodeData.transform.from;
        transform.to = nodeData.transform.to;
      }

      // 将转换添加到列表中
      config.dataSource.transforms.push(transform);

      // 如果有描述，添加到配置中
      if (nodeData.description) {
        transform.description = nodeData.description;
      }
      break;

    case "data-mapping":
      // 处理CSV映射文件配置
      if (!config.dataSource) {
        config.dataSource = {};
      }

      // 设置映射文件路径
      config.dataSource.mappings =
        nodeData.mappingFile || "public/config/mappings/mapping_1-3-1.csv";

      // 设置匹配条件，如果存在
      if (nodeData.match) {
        if (!config.dataSource.transforms) {
          config.dataSource.transforms = [];
        }

        // 添加匹配转换配置
        const matchTransform = {
          type: "match",
          match: nodeData.match,
          description: nodeData.description || "CSV映射匹配条件",
        };

        config.dataSource.transforms.push(matchTransform);
      }
      break;

    case "data-consumer":
      // 处理数据消费配置

      // 设置回调和参数
      config.callback = nodeData.callback || "";
      config.parameters = nodeData.parameters || {};

      // 如果有触发条件，添加到配置中
      if (nodeData.triggerCondition) {
        if (!config.parameters) {
          config.parameters = {};
        }

        // 根据触发条件类型设置参数
        switch (nodeData.triggerCondition) {
          case "onChange":
            config.parameters.triggerOnChange = true;
            break;
          case "always":
            config.parameters.triggerAlways = true;
            break;
          case "onTrue":
            config.parameters.triggerOnTrue = true;
            break;
          case "onFalse":
            config.parameters.triggerOnFalse = true;
            break;
        }
      }

      // 如果有描述，添加到配置中
      if (nodeData.description) {
        config.description = nodeData.description;
      }
      break;

    default:
      return null;
  }

  // 查找下游节点并继续处理
  const nextNodes = findNextNodes(node.id, "out-exec", allNodes, allEdges);
  if (nextNodes.length > 0) {
    const nextConfig = processChainingNodes(
      nextNodes[0],
      allNodes,
      allEdges,
      processedNodes
    );
    if (nextConfig) {
      // 合并下游配置
      Object.assign(config, nextConfig);
    }
  }

  // 如果是数据源节点，处理数据输出端口的下游节点
  if (
    nodeData.nodeType === "data-source" ||
    nodeData.nodeType === "data-transform" ||
    nodeData.nodeType === "data-mapping"
  ) {
    const dataOutNodes = findNextNodes(node.id, "out-data", allNodes, allEdges);
    if (dataOutNodes.length > 0) {
      // 存储数据转换配置
      const transforms = [];

      // 处理每个数据转换节点
      for (const dataNode of dataOutNodes) {
        const transformConfig = processChainingNodes(
          dataNode,
          allNodes,
          allEdges,
          new Set(processedNodes)
        );

        if (transformConfig) {
          transforms.push(transformConfig);
        }
      }

      // 如果有转换配置，添加到数据源配置
      if (transforms.length > 0) {
        if (nodeData.nodeType === "data-source") {
          // 数据源节点的转换
          if (!config.dataSource) config.dataSource = {};
          if (!config.dataSource.transforms) config.dataSource.transforms = [];

          // 收集所有转换
          transforms.forEach((t) => {
            if (t.dataSource && t.dataSource.transforms) {
              config.dataSource.transforms =
                config.dataSource.transforms.concat(t.dataSource.transforms);
            }
          });
        } else {
          // 数据转换节点的后续转换，将它们合并到当前转换列表中
          transforms.forEach((t) => {
            if (t.dataSource && t.dataSource.transforms) {
              if (!config.dataSource) config.dataSource = {};
              if (!config.dataSource.transforms)
                config.dataSource.transforms = [];
              config.dataSource.transforms =
                config.dataSource.transforms.concat(t.dataSource.transforms);
            }
          });
        }
      }
    }
  }

  return config;
}

/**
 * 查找节点特定端口的下游连接节点
 */
function findNextNodes(
  nodeId: string,
  portId: string | null,
  allNodes: Cell.Properties[],
  allEdges: Cell.Properties[]
): Cell.Properties[] {
  // 找出从该节点指定端口出发的所有边
  const outEdges = allEdges.filter((edge: Cell.Properties) => {
    const source = edge.source as { cell: string; port: string } | undefined;
    return (
      source && source.cell === nodeId && (!portId || source.port === portId)
    );
  });

  // 找出这些边连接到的目标节点
  const targetNodeIds = outEdges
    .map((edge: Cell.Properties) => {
      const target = edge.target as { cell: string; port: string } | undefined;
      return target?.cell;
    })
    .filter((id): id is string => id !== undefined); // 过滤掉undefined的ID

  // 返回目标节点对象数组
  return allNodes.filter((node: Cell.Properties) =>
    targetNodeIds.includes(node.id)
  );
}

/**
 * 查找节点特定端口的上游连接节点
 */
function findPrevNodes(
  nodeId: string,
  portId: string | null,
  allNodes: Cell.Properties[],
  allEdges: Cell.Properties[]
): Cell.Properties[] {
  // 找出指向该节点指定端口的所有边
  const inEdges = allEdges.filter((edge: Cell.Properties) => {
    const target = edge.target as { cell: string; port: string } | undefined;
    return (
      target && target.cell === nodeId && (!portId || target.port === portId)
    );
  });

  // 找出这些边的源节点
  const sourceNodeIds = inEdges
    .map((edge: Cell.Properties) => {
      const source = edge.source as { cell: string; port: string } | undefined;
      return source?.cell;
    })
    .filter((id): id is string => id !== undefined); // 过滤掉undefined的ID

  // 返回源节点对象数组
  return allNodes.filter((node: Cell.Properties) =>
    sourceNodeIds.includes(node.id)
  );
}
