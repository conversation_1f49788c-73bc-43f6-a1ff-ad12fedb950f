<template>
  <div class="search-filter">
    <div class="search-input-wrapper">
      <input
        v-model="searchQuery"
        type="text"
        class="search-input"
        :placeholder="placeholder"
        @input="handleSearch"
        @keydown.escape="clearSearch"
      />
      <button
        v-if="searchQuery"
        class="clear-btn"
        @click="clearSearch"
        title="清除搜索"
      >
        ✕
      </button>
    </div>
    
    <div v-if="showFilters" class="filters">
      <div class="filter-group">
        <label class="filter-label">类型过滤:</label>
        <div class="filter-options">
          <button
            v-for="filter in availableFilters"
            :key="filter.value"
            class="filter-btn"
            :class="{ active: activeFilters.includes(filter.value) }"
            @click="toggleFilter(filter.value)"
          >
            {{ filter.label }}
          </button>
        </div>
      </div>
      
      <div class="filter-actions">
        <button class="filter-action-btn" @click="clearAllFilters">
          清除所有过滤
        </button>
        <button class="filter-action-btn" @click="selectAllFilters">
          选择所有
        </button>
      </div>
    </div>
    
    <div v-if="showResults" class="search-results">
      <div class="results-header">
        <span class="results-count">
          找到 {{ filteredResults.length }} 个结果
        </span>
        <button
          class="toggle-filters-btn"
          @click="showFilters = !showFilters"
        >
          {{ showFilters ? '隐藏过滤器' : '显示过滤器' }}
        </button>
      </div>
      
      <div class="results-list">
        <div
          v-for="(result, index) in paginatedResults"
          :key="result.id || index"
          class="result-item"
          :class="{ selected: selectedResult?.id === result.id }"
          @click="selectResult(result)"
        >
          <div class="result-content">
            <div class="result-title">{{ result.title || result.name }}</div>
            <div v-if="result.description" class="result-description">
              {{ result.description }}
            </div>
            <div class="result-meta">
              <span class="result-type">{{ result.type }}</span>
              <span v-if="result.category" class="result-category">
                {{ result.category }}
              </span>
            </div>
          </div>
          <div class="result-actions">
            <button
              class="result-action-btn"
              @click.stop="$emit('action', { action: 'select', item: result })"
              title="选择"
            >
              ✓
            </button>
            <button
              v-if="result.editable"
              class="result-action-btn"
              @click.stop="$emit('action', { action: 'edit', item: result })"
              title="编辑"
            >
              ✎
            </button>
          </div>
        </div>
      </div>
      
      <div v-if="totalPages > 1" class="pagination">
        <button
          class="pagination-btn"
          :disabled="currentPage === 1"
          @click="currentPage = 1"
        >
          首页
        </button>
        <button
          class="pagination-btn"
          :disabled="currentPage === 1"
          @click="currentPage--"
        >
          上一页
        </button>
        <span class="pagination-info">
          第 {{ currentPage }} 页，共 {{ totalPages }} 页
        </span>
        <button
          class="pagination-btn"
          :disabled="currentPage === totalPages"
          @click="currentPage++"
        >
          下一页
        </button>
        <button
          class="pagination-btn"
          :disabled="currentPage === totalPages"
          @click="currentPage = totalPages"
        >
          末页
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface SearchResult {
  id: string
  title?: string
  name?: string
  description?: string
  type: string
  category?: string
  editable?: boolean
  [key: string]: any
}

interface FilterOption {
  value: string
  label: string
}

interface Props {
  items: SearchResult[]
  placeholder?: string
  filters?: FilterOption[]
  pageSize?: number
  searchFields?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '搜索...',
  filters: () => [],
  pageSize: 10,
  searchFields: () => ['title', 'name', 'description', 'type']
})

const emit = defineEmits<{
  search: [query: string]
  filter: [filters: string[]]
  select: [item: SearchResult]
  action: [data: { action: string; item: SearchResult }]
}>()

// 搜索状态
const searchQuery = ref('')
const activeFilters = ref<string[]>([])
const selectedResult = ref<SearchResult | null>(null)
const showFilters = ref(false)
const showResults = ref(false)
const currentPage = ref(1)

// 可用过滤器
const availableFilters = computed(() => {
  if (props.filters.length > 0) {
    return props.filters
  }
  
  // 自动生成过滤器
  const types = new Set(props.items.map(item => item.type))
  return Array.from(types).map(type => ({
    value: type,
    label: type
  }))
})

// 过滤后的结果
const filteredResults = computed(() => {
  let results = props.items

  // 应用搜索查询
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    results = results.filter(item => {
      return props.searchFields.some(field => {
        const value = item[field]
        return value && value.toString().toLowerCase().includes(query)
      })
    })
  }

  // 应用过滤器
  if (activeFilters.value.length > 0) {
    results = results.filter(item => 
      activeFilters.value.includes(item.type)
    )
  }

  return results
})

// 分页结果
const totalPages = computed(() => 
  Math.ceil(filteredResults.value.length / props.pageSize)
)

const paginatedResults = computed(() => {
  const start = (currentPage.value - 1) * props.pageSize
  const end = start + props.pageSize
  return filteredResults.value.slice(start, end)
})

// 搜索处理
function handleSearch() {
  showResults.value = true
  currentPage.value = 1
  emit('search', searchQuery.value)
}

// 清除搜索
function clearSearch() {
  searchQuery.value = ''
  showResults.value = false
  selectedResult.value = null
  emit('search', '')
}

// 切换过滤器
function toggleFilter(filterValue: string) {
  const index = activeFilters.value.indexOf(filterValue)
  if (index > -1) {
    activeFilters.value.splice(index, 1)
  } else {
    activeFilters.value.push(filterValue)
  }
  currentPage.value = 1
}

// 清除所有过滤器
function clearAllFilters() {
  activeFilters.value = []
  currentPage.value = 1
}

// 选择所有过滤器
function selectAllFilters() {
  activeFilters.value = availableFilters.value.map(f => f.value)
  currentPage.value = 1
}

// 选择结果
function selectResult(result: SearchResult) {
  selectedResult.value = result
  emit('select', result)
}

// 监听过滤器变化
watch(activeFilters, () => {
  emit('filter', activeFilters.value)
}, { deep: true })

// 监听搜索查询变化
watch(searchQuery, (newQuery) => {
  if (newQuery.trim()) {
    showResults.value = true
  }
})
</script>

<style scoped>
.search-filter {
  width: 100%;
  background: #2a2a2a;
  border-radius: 6px;
  padding: 12px;
}

.search-input-wrapper {
  position: relative;
  margin-bottom: 12px;
}

.search-input {
  width: 100%;
  padding: 8px 32px 8px 12px;
  background: #3a3a3a;
  border: 1px solid #555;
  border-radius: 4px;
  color: #e0e0e0;
  font-size: 14px;
}

.search-input:focus {
  outline: none;
  border-color: #1890ff;
}

.clear-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  font-size: 16px;
}

.clear-btn:hover {
  color: #e0e0e0;
}

.filters {
  margin-bottom: 12px;
  padding: 12px;
  background: #333;
  border-radius: 4px;
}

.filter-label {
  display: block;
  color: #e0e0e0;
  font-size: 12px;
  margin-bottom: 8px;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 12px;
}

.filter-btn {
  padding: 4px 8px;
  background: #4a4a4a;
  border: 1px solid #666;
  border-radius: 3px;
  color: #e0e0e0;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-btn:hover {
  background: #5a5a5a;
}

.filter-btn.active {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}

.filter-actions {
  display: flex;
  gap: 8px;
}

.filter-action-btn {
  padding: 4px 8px;
  background: #555;
  border: 1px solid #666;
  border-radius: 3px;
  color: #e0e0e0;
  font-size: 11px;
  cursor: pointer;
}

.filter-action-btn:hover {
  background: #666;
}

.search-results {
  max-height: 400px;
  overflow-y: auto;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #444;
}

.results-count {
  color: #999;
  font-size: 12px;
}

.toggle-filters-btn {
  padding: 4px 8px;
  background: #555;
  border: 1px solid #666;
  border-radius: 3px;
  color: #e0e0e0;
  font-size: 11px;
  cursor: pointer;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  margin-bottom: 4px;
  background: #3a3a3a;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.2s;
}

.result-item:hover {
  background: #4a4a4a;
}

.result-item.selected {
  background: #1890ff;
  color: white;
}

.result-content {
  flex: 1;
}

.result-title {
  font-weight: bold;
  margin-bottom: 2px;
}

.result-description {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.result-meta {
  display: flex;
  gap: 8px;
  font-size: 11px;
}

.result-type,
.result-category {
  padding: 2px 6px;
  background: #555;
  border-radius: 2px;
  color: #ccc;
}

.result-actions {
  display: flex;
  gap: 4px;
}

.result-action-btn {
  padding: 4px 6px;
  background: #555;
  border: 1px solid #666;
  border-radius: 3px;
  color: #e0e0e0;
  font-size: 12px;
  cursor: pointer;
}

.result-action-btn:hover {
  background: #666;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #444;
}

.pagination-btn {
  padding: 4px 8px;
  background: #555;
  border: 1px solid #666;
  border-radius: 3px;
  color: #e0e0e0;
  font-size: 12px;
  cursor: pointer;
}

.pagination-btn:hover:not(:disabled) {
  background: #666;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  color: #999;
  font-size: 12px;
}
</style>
