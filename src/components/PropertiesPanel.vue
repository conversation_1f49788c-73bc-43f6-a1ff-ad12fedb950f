<script setup lang="ts">
import { computed, ref, watch } from "vue";

const props = defineProps<{
  selectedNode: any | null;
}>();

const emit = defineEmits(["update-node", "change-node-type"]);

// 本地状态，用于编辑
const nodeData = ref<any>(null);

// 用于编辑JSON的文本
const headersText = ref("");
const paramsText = ref("");
const mappingText = ref(""); // 新增映射文本

// 可用的节点类型映射
const nodeTypes = [
  { value: "event", label: "事件节点" },
  { value: "action-highlight", label: "高亮动作" },
  { value: "action-callback", label: "回调动作" },
  { value: "logic-branch", label: "条件分支" },
  { value: "scene-config", label: "场景配置" },
  { value: "data-source", label: "数据源" },
  { value: "data-transform", label: "数据转换" },
  { value: "data-mapping", label: "CSV映射文件" },
  { value: "data-consumer", label: "数据消费" },
  { value: "lifecycle", label: "生命周期" },
];

// 监听外部传入的节点变化
watch(
  () => props.selectedNode,
  (newNode) => {
    if (newNode) {
      // 创建深拷贝避免直接修改原对象
      nodeData.value = JSON.parse(JSON.stringify(newNode.data || {}));

      // 如果是数据源节点，初始化JSON文本
      if (nodeData.value?.nodeType === "data-source") {
        initDataSourceTextFields();
      }
    } else {
      nodeData.value = null;
      headersText.value = "";
      paramsText.value = "";
    }
  },
  { immediate: true }
);

// 初始化数据源文本字段
function initDataSourceTextFields() {
  if (nodeData.value?.dataSource) {
    if (nodeData.value.dataSource.headers) {
      headersText.value = JSON.stringify(
        nodeData.value.dataSource.headers,
        null,
        2
      );
    } else {
      headersText.value = "{}";
    }

    if (nodeData.value.dataSource.params) {
      paramsText.value = JSON.stringify(
        nodeData.value.dataSource.params,
        null,
        2
      );
    } else {
      paramsText.value = "{}";
    }
  }

  // 初始化映射关系文本
  if (
    nodeData.value?.nodeType === "data-transform" &&
    nodeData.value.transform?.mapping
  ) {
    mappingText.value = JSON.stringify(
      nodeData.value.transform.mapping,
      null,
      2
    );
  }
}

// 更新请求头
function updateHeadersFromText() {
  try {
    if (nodeData.value?.dataSource) {
      nodeData.value.dataSource.headers = JSON.parse(headersText.value);
      updateNodeData();
    }
  } catch (e) {
    console.error("解析请求头JSON失败:", e);
  }
}

// 更新请求参数
function updateParamsFromText() {
  try {
    if (nodeData.value?.dataSource) {
      nodeData.value.dataSource.params = JSON.parse(paramsText.value);
      updateNodeData();
    }
  } catch (e) {
    console.error("解析请求参数JSON失败:", e);
  }
}

// 更新数据源类型
function updateDataSourceType() {
  if (!nodeData.value) return;

  if (nodeData.value.sourceType === "polling") {
    nodeData.value.dataSource = {
      type: "polling",
      interval: 5000,
      url: "http://127.0.0.1:4523/api/data",
      method: "get",
      params: {},
      headers: {
        "Content-Type": "application/json",
      },
    };
    nodeData.value.displayName = "轮询数据源";
  } else if (nodeData.value.sourceType === "websocket") {
    nodeData.value.dataSource = {
      type: "websocket",
      url: "ws://127.0.0.1:8080",
      reconnectInterval: 3000,
      maxRetries: 5,
    };
    nodeData.value.displayName = "WebSocket数据源";
  }

  initDataSourceTextFields();
  updateNodeData();
}

// 更新映射关系
function updateMappingFromText() {
  try {
    if (nodeData.value?.transform) {
      nodeData.value.transform.mapping = JSON.parse(mappingText.value);
      updateNodeData();
    }
  } catch (e) {
    console.error("解析映射JSON失败:", e);
  }
}

// 更新转换类型
function updateTransformType() {
  if (!nodeData.value) return;

  if (nodeData.value.transformType === "map") {
    nodeData.value.transform = {
      type: "map",
      input: "value",
      output: "transformedValue",
      mapping: {
        "0": "关",
        "1": "开",
      },
    };
    nodeData.value.displayName = "映射转换";
    mappingText.value = JSON.stringify(
      nodeData.value.transform.mapping,
      null,
      2
    );
  } else if (nodeData.value.transformType === "range") {
    nodeData.value.transform = {
      type: "range",
      input: "value",
      output: "normalizedValue",
      from: [0, 100],
      to: [0, 1],
    };
    nodeData.value.displayName = "范围转换";
  }

  updateNodeData();
}

// 根据节点类型计算显示的表单类型
const nodeType = computed(() => {
  if (!nodeData.value) return null;
  return nodeData.value.nodeType;
});

// 当表单数据变化时更新节点
function updateNodeData() {
  if (props.selectedNode) {
    emit("update-node", {
      nodeId: props.selectedNode.id,
      data: nodeData.value,
    });
  }
}

// 切换节点类型
function changeNodeType(newType: string) {
  if (props.selectedNode && nodeData.value) {
    // 保留原节点的通用数据（但不包括显示名称）
    const oldData = nodeData.value;

    // 根据新类型创建初始化数据，不包含原来的显示名称
    let newData: any = {
      nodeType: newType,
    };

    // 根据新节点类型设置特定属性和默认显示名称
    switch (newType) {
      case "event":
        newData.actionType = "doubleClick";
        newData.description = "双击触发";
        newData.displayName = "双击事件"; // 默认显示名称
        break;
      case "action-highlight":
        newData.color = [1, 1, 0];
        newData.duration = 1500;
        newData.displayName = "高亮"; // 默认显示名称
        break;
      case "action-callback":
        newData.callback = "CameraService.focusToDevice";
        newData.parameters = {
          deviceName: "{{meshName}}",
          duration: 120,
          paddingFactor: 1,
        };
        newData.displayName = "相机聚焦"; // 默认显示名称
        break;
      case "logic-branch":
        newData.conditionProperty = "meshName";
        newData.conditionOperator = "==";
        newData.conditionValue = "";
        newData.displayName = "条件分支"; // 默认显示名称
        break;
      case "scene-config":
        newData.sceneId = oldData.sceneId || "1";
        newData.sceneName = oldData.sceneName || "默认场景";
        newData.models = oldData.models || ["陆地开关站/陆地开关站楼.glb"];
        newData.environment = oldData.environment || "techStyle";
        newData.displayName = `场景: ${newData.sceneName}`; // 默认显示名称
        break;
      case "data-source":
        newData.sourceType = "polling";
        newData.dataSource = {
          type: "polling",
          interval: 5000,
          url: "http://127.0.0.1:4523/api/data",
          method: "get",
          params: {},
          headers: {
            "Content-Type": "application/json",
          },
        };
        newData.displayName = "轮询数据源"; // 默认显示名称
        break;
      case "data-transform":
        newData.transformType = "map";
        newData.transform = {
          type: "map",
          input: "value",
          output: "transformedValue",
          mapping: {
            "0": "关",
            "1": "开",
          },
        };
        newData.displayName = "映射转换"; // 默认显示名称
        break;
      case "data-mapping":
        newData.mappingFile = "public/config/mappings/mapping_1-3-1.csv";
        newData.match = {
          pointType: "digital",
          meshType: "分合状态",
        };
        newData.displayName = "CSV映射文件"; // 默认显示名称
        break;
      case "data-consumer":
        newData.consumerType = "toggle";
        newData.callback = "AnimationService.toggleModelVisibility";
        newData.parameters = {
          meshName: "{{meshName}}",
          isVisible: "{{meshValue}}",
        };
        newData.triggerCondition = "onChange";
        newData.displayName = "切换可见性"; // 默认显示名称
        break;
      case "lifecycle":
        newData.lifecycleType = "onActivated";
        newData.trigger = "immediate";
        newData.description = "场景激活时触发";
        newData.displayName = "场景激活"; // 默认显示名称
        break;
    }

    // 更新本地数据
    nodeData.value = newData;

    // 触发节点类型变更事件
    emit("change-node-type", {
      nodeId: props.selectedNode.id,
      oldType: oldData.nodeType,
      newType: newType,
      newData: newData,
    });
  }
}

// 添加模型路径
function addModel() {
  if (nodeData.value && nodeData.value.models) {
    nodeData.value.models.push("");
    updateNodeData();
  }
}

// 移除模型路径
function removeModel(index: number) {
  if (nodeData.value && nodeData.value.models) {
    nodeData.value.models.splice(index, 1);
    updateNodeData();
  }
}

// 更新数据消费类型
function updateConsumerType() {
  if (!nodeData.value) return;

  if (nodeData.value.consumerType === "toggle") {
    nodeData.value.callback = "AnimationService.toggleModelVisibility";
    nodeData.value.parameters = {
      meshName: "{{meshName}}",
      isVisible: "{{meshValue}}",
    };
    nodeData.value.displayName = "切换可见性";
  } else if (nodeData.value.consumerType === "animate") {
    nodeData.value.callback = "AnimationService.playMeshAnimation";
    nodeData.value.parameters = {
      meshName: "{{meshName}}",
      speed: "{{meshValue}}",
      loop: false,
    };
    nodeData.value.displayName = "动画控制";
  } else if (nodeData.value.consumerType === "property") {
    nodeData.value.callback = "UIService.setProperty";
    nodeData.value.parameters = {
      meshName: "{{meshName}}",
      property: "emissiveColor",
      value: "{{meshValue}}",
    };
    nodeData.value.displayName = "属性控制";
  }
  updateNodeData();
}
</script>

<template>
  <div class="properties-panel">
    <h3>属性面板</h3>

    <div v-if="selectedNode" class="panel-content">
      <div class="node-id">节点ID: {{ selectedNode.id }}</div>

      <!-- 动态表单区域 -->
      <template v-if="nodeData">
        <div class="form-container">
          <!-- 通用设置 - 所有节点类型通用 -->
          <div class="form-section">
            <h4>通用设置</h4>
            <div class="form-group">
              <label>节点类型:</label>
              <select
                v-model="nodeData.nodeType"
                @change="(e) => changeNodeType((e.target as HTMLSelectElement).value)"
              >
                <option
                  v-for="type in nodeTypes"
                  :key="type.value"
                  :value="type.value"
                >
                  {{ type.label }}
                </option>
              </select>
            </div>
            <div class="form-group">
              <label>显示名称:</label>
              <input
                type="text"
                v-model="nodeData.displayName"
                @input="updateNodeData"
                @blur="updateNodeData"
                placeholder="节点在画布中的显示名称"
              />
            </div>
          </div>

          <div v-if="nodeType === 'event'" class="form-section">
            <h4>事件节点设置</h4>
            <div class="form-group">
              <label>事件类型:</label>
              <select v-model="nodeData.actionType" @change="updateNodeData">
                <option value="doubleClick">双击 (Double Click)</option>
                <option value="click">单击 (Click)</option>
                <option value="hover">悬停 (Hover)</option>
                <option value="rightDoubleClick">
                  右键双击 (Right Double Click)
                </option>
              </select>
            </div>
            <div class="form-group">
              <label>描述:</label>
              <input
                type="text"
                v-model="nodeData.description"
                @blur="updateNodeData"
                placeholder="节点描述"
              />
            </div>
          </div>

          <div v-else-if="nodeType === 'action-highlight'" class="form-section">
            <h4>高亮设置</h4>
            <div class="form-group">
              <label>高亮颜色 [R,G,B]:</label>
              <div class="color-inputs">
                <input
                  type="number"
                  min="0"
                  max="1"
                  step="0.1"
                  v-model="nodeData.color[0]"
                  @blur="updateNodeData"
                  placeholder="R (0-1)"
                />
                <input
                  type="number"
                  min="0"
                  max="1"
                  step="0.1"
                  v-model="nodeData.color[1]"
                  @blur="updateNodeData"
                  placeholder="G (0-1)"
                />
                <input
                  type="number"
                  min="0"
                  max="1"
                  step="0.1"
                  v-model="nodeData.color[2]"
                  @blur="updateNodeData"
                  placeholder="B (0-1)"
                />
              </div>
            </div>
            <div class="form-group">
              <label>持续时间 (ms):</label>
              <input
                type="number"
                v-model="nodeData.duration"
                @blur="updateNodeData"
                placeholder="留空表示持久高亮"
              />
            </div>
          </div>

          <div v-else-if="nodeType === 'action-callback'" class="form-section">
            <h4>回调设置</h4>
            <div class="form-group">
              <label>服务方法:</label>
              <select v-model="nodeData.callback" @change="updateNodeData">
                <option value="CameraService.focusToDevice">
                  相机聚焦 (focusToDevice)
                </option>
                <option value="CameraService.moveCamera">
                  相机移动 (moveCamera)
                </option>
                <option value="AnimationService.playMeshAnimation">
                  播放动画 (playMeshAnimation)
                </option>
                <option value="UIService.showMessage">
                  显示消息 (showMessage)
                </option>
              </select>
            </div>

            <template
              v-if="nodeData.callback === 'CameraService.focusToDevice'"
            >
              <div class="form-group">
                <label>参数设置:</label>
                <div class="param-group">
                  <label>设备名:</label>
                  <input
                    type="text"
                    v-model="nodeData.parameters.deviceName"
                    @blur="updateNodeData"
                    placeholder="{{meshName}}"
                  />
                </div>
                <div class="param-group">
                  <label>持续时间:</label>
                  <input
                    type="number"
                    v-model="nodeData.parameters.duration"
                    @blur="updateNodeData"
                    placeholder="120"
                  />
                </div>
                <div class="param-group">
                  <label>填充因子:</label>
                  <input
                    type="number"
                    step="0.1"
                    v-model="nodeData.parameters.paddingFactor"
                    @blur="updateNodeData"
                    placeholder="1.0"
                  />
                </div>
              </div>
            </template>
          </div>

          <div v-else-if="nodeType === 'logic-branch'" class="form-section">
            <h4>条件分支设置</h4>
            <div class="form-group">
              <label>描述:</label>
              <input
                type="text"
                v-model="nodeData.description"
                @blur="updateNodeData"
                placeholder="节点描述"
              />
            </div>
            <div class="form-group">
              <label>条件属性:</label>
              <input
                type="text"
                v-model="nodeData.conditionProperty"
                @blur="updateNodeData"
                placeholder="meshName"
              />
            </div>
            <div class="form-group">
              <label>条件运算符:</label>
              <select
                v-model="nodeData.conditionOperator"
                @change="updateNodeData"
              >
                <option value="==">等于 (==)</option>
                <option value="!=">不等于 (!=)</option>
                <option value=">">大于 (>)</option>
                <option value="<">小于 (<)</option>
                <option value=">=">大于等于 (>=)</option>
                <option value="<=">小于等于 (<=)</option>
              </select>
            </div>
            <div class="form-group">
              <label>条件值:</label>
              <input
                type="text"
                v-model="nodeData.conditionValue"
                @blur="updateNodeData"
                placeholder="条件值"
              />
            </div>
          </div>

          <div v-else-if="nodeType === 'scene-config'" class="form-section">
            <h4>场景配置</h4>
            <div class="form-group">
              <label>场景ID:</label>
              <input
                type="text"
                v-model="nodeData.sceneId"
                @blur="updateNodeData"
                placeholder="1-3-1"
              />
            </div>
            <div class="form-group">
              <label>场景名称:</label>
              <input
                type="text"
                v-model="nodeData.sceneName"
                @blur="updateNodeData"
                placeholder="场景名称"
              />
            </div>
            <div class="form-group">
              <label>模型路径:</label>
              <div
                v-for="(model, index) in nodeData.models"
                :key="index"
                class="array-item"
              >
                <input
                  type="text"
                  v-model="nodeData.models[index]"
                  @blur="updateNodeData"
                  placeholder="模型路径"
                />
                <button @click="removeModel(index)" class="remove-btn">
                  -
                </button>
              </div>
              <button @click="addModel" class="add-btn">添加模型</button>
            </div>
            <div class="form-group">
              <label>环境模板:</label>
              <select v-model="nodeData.environment" @change="updateNodeData">
                <option value="techStyle">科技风格 (techStyle)</option>
                <option value="basic">基础环境 (basic)</option>
                <option value="water">水环境 (water)</option>
              </select>
            </div>
            <div class="form-group">
              <label>描述:</label>
              <input
                type="text"
                v-model="nodeData.description"
                @blur="updateNodeData"
                placeholder="场景描述"
              />
            </div>
          </div>

          <div v-else-if="nodeType === 'data-source'" class="form-section">
            <h4>数据源配置</h4>
            <div class="form-group">
              <label>数据源类型:</label>
              <select
                v-model="nodeData.sourceType"
                @change="updateDataSourceType"
              >
                <option value="polling">轮询 (Polling)</option>
                <option value="websocket">WebSocket</option>
              </select>
            </div>

            <!-- 轮询数据源配置 -->
            <template v-if="nodeData.sourceType === 'polling'">
              <div class="form-group">
                <label>请求URL:</label>
                <input
                  type="text"
                  v-model="nodeData.dataSource.url"
                  @blur="updateNodeData"
                  placeholder="http://127.0.0.1:4523/api/data"
                />
              </div>
              <div class="form-group">
                <label>请求方法:</label>
                <select
                  v-model="nodeData.dataSource.method"
                  @change="updateNodeData"
                >
                  <option value="get">GET</option>
                  <option value="post">POST</option>
                  <option value="put">PUT</option>
                </select>
              </div>
              <div class="form-group">
                <label>轮询间隔 (ms):</label>
                <input
                  type="number"
                  v-model="nodeData.dataSource.interval"
                  @blur="updateNodeData"
                  placeholder="5000"
                />
              </div>
              <div class="form-group">
                <label>请求头:</label>
                <div class="code-editor">
                  <textarea
                    v-model="headersText"
                    @blur="updateHeadersFromText"
                    placeholder='{ "Content-Type": "application/json" }'
                    rows="4"
                  ></textarea>
                </div>
              </div>
              <div class="form-group">
                <label>请求参数:</label>
                <div class="code-editor">
                  <textarea
                    v-model="paramsText"
                    @blur="updateParamsFromText"
                    placeholder='{ "key": "value" }'
                    rows="4"
                  ></textarea>
                </div>
              </div>
            </template>

            <!-- WebSocket数据源配置 -->
            <template v-else-if="nodeData.sourceType === 'websocket'">
              <div class="form-group">
                <label>WebSocket URL:</label>
                <input
                  type="text"
                  v-model="nodeData.dataSource.url"
                  @blur="updateNodeData"
                  placeholder="ws://127.0.0.1:8080"
                />
              </div>
              <div class="form-group">
                <label>重连间隔 (ms):</label>
                <input
                  type="number"
                  v-model="nodeData.dataSource.reconnectInterval"
                  @blur="updateNodeData"
                  placeholder="3000"
                />
              </div>
              <div class="form-group">
                <label>最大重试次数:</label>
                <input
                  type="number"
                  v-model="nodeData.dataSource.maxRetries"
                  @blur="updateNodeData"
                  placeholder="5"
                />
              </div>
            </template>

            <div class="form-group">
              <label>描述:</label>
              <input
                type="text"
                v-model="nodeData.description"
                @blur="updateNodeData"
                placeholder="数据源描述"
              />
            </div>
          </div>

          <div v-else-if="nodeType === 'data-transform'" class="form-section">
            <h4>数据转换设置</h4>
            <div class="form-group">
              <label>转换类型:</label>
              <select
                v-model="nodeData.transformType"
                @change="updateTransformType"
              >
                <option value="map">映射转换 (Map)</option>
                <option value="range">范围转换 (Range)</option>
              </select>
            </div>

            <div class="form-group">
              <label>输入字段:</label>
              <input
                type="text"
                v-model="nodeData.transform.input"
                @blur="updateNodeData"
                placeholder="value"
              />
            </div>
            <div class="form-group">
              <label>输出字段:</label>
              <input
                type="text"
                v-model="nodeData.transform.output"
                @blur="updateNodeData"
                placeholder="transformedValue"
              />
            </div>

            <!-- 映射转换特定设置 -->
            <template v-if="nodeData.transformType === 'map'">
              <div class="form-group">
                <label>映射关系:</label>
                <div class="code-editor">
                  <textarea
                    v-model="mappingText"
                    @blur="updateMappingFromText"
                    placeholder='{ "0": "关", "1": "开" }'
                    rows="6"
                  ></textarea>
                </div>
              </div>
            </template>

            <!-- 范围转换特定设置 -->
            <template v-else-if="nodeData.transformType === 'range'">
              <div class="form-group">
                <label>输入范围:</label>
                <div class="range-inputs">
                  <input
                    type="number"
                    v-model.number="nodeData.transform.from[0]"
                    @blur="updateNodeData"
                    placeholder="最小值"
                  />
                  <span>-</span>
                  <input
                    type="number"
                    v-model.number="nodeData.transform.from[1]"
                    @blur="updateNodeData"
                    placeholder="最大值"
                  />
                </div>
              </div>
              <div class="form-group">
                <label>输出范围:</label>
                <div class="range-inputs">
                  <input
                    type="number"
                    v-model.number="nodeData.transform.to[0]"
                    @blur="updateNodeData"
                    placeholder="最小值"
                  />
                  <span>-</span>
                  <input
                    type="number"
                    v-model.number="nodeData.transform.to[1]"
                    @blur="updateNodeData"
                    placeholder="最大值"
                  />
                </div>
              </div>
            </template>

            <div class="form-group">
              <label>描述:</label>
              <input
                type="text"
                v-model="nodeData.description"
                @blur="updateNodeData"
                placeholder="转换描述"
              />
            </div>
          </div>

          <div v-else-if="nodeType === 'data-mapping'" class="form-section">
            <h4>CSV映射文件配置</h4>
            <div class="form-group">
              <label>映射文件路径:</label>
              <select v-model="nodeData.mappingFile" @change="updateNodeData">
                <option value="public/config/mappings/mapping_1-3-1.csv">
                  mapping_1-3-1.csv
                </option>
                <option value="public/config/mappings/custom.csv">
                  custom.csv
                </option>
              </select>
            </div>

            <div class="form-group">
              <label>匹配条件:</label>
              <div class="match-container">
                <div class="match-group">
                  <label>数据类型:</label>
                  <select
                    v-model="nodeData.match.pointType"
                    @change="updateNodeData"
                  >
                    <option value="digital">数字量 (digital)</option>
                    <option value="analog">模拟量 (analog)</option>
                  </select>
                </div>
                <div class="match-group">
                  <label>网格类型:</label>
                  <input
                    type="text"
                    v-model="nodeData.match.meshType"
                    @blur="updateNodeData"
                    placeholder="分合状态"
                  />
                </div>
              </div>
            </div>

            <div class="form-group file-preview">
              <label>文件预览:</label>
              <div class="file-preview-content">
                <div class="file-preview-row header">
                  <div class="file-preview-cell">pointKey</div>
                  <div class="file-preview-cell">meshName</div>
                  <div class="file-preview-cell">meshType</div>
                  <div class="file-preview-cell">pointType</div>
                </div>
                <div class="file-preview-row">
                  <div class="file-preview-cell">GIS_STATUS_001</div>
                  <div class="file-preview-cell">DOOR_NA_LSZ_GIS_MOD105</div>
                  <div class="file-preview-cell">分合状态</div>
                  <div class="file-preview-cell">digital</div>
                </div>
                <div class="file-preview-row">
                  <div class="file-preview-cell">GIS_STATUS_002</div>
                  <div class="file-preview-cell">DOOR_NA_LSZ_GIS_MOD108</div>
                  <div class="file-preview-cell">分合状态</div>
                  <div class="file-preview-cell">digital</div>
                </div>
                <div class="file-preview-row">
                  <div class="file-preview-cell">...</div>
                  <div class="file-preview-cell">...</div>
                  <div class="file-preview-cell">...</div>
                  <div class="file-preview-cell">...</div>
                </div>
              </div>
              <p class="file-preview-note">
                * 显示前3行预览，实际文件可能包含更多数据
              </p>
            </div>

            <div class="form-group">
              <label>描述:</label>
              <input
                type="text"
                v-model="nodeData.description"
                @blur="updateNodeData"
                placeholder="CSV映射文件配置"
              />
            </div>
          </div>

          <div v-else-if="nodeType === 'data-consumer'" class="form-section">
            <h4>数据消费配置</h4>
            <div class="form-group">
              <label>消费类型:</label>
              <select
                v-model="nodeData.consumerType"
                @change="updateConsumerType"
              >
                <option value="toggle">切换可见性</option>
                <option value="animate">动画控制</option>
                <option value="property">属性控制</option>
              </select>
            </div>

            <div class="form-group">
              <label>回调函数:</label>
              <input
                type="text"
                v-model="nodeData.callback"
                @blur="updateNodeData"
                placeholder="AnimationService.toggleModelVisibility"
              />
            </div>

            <div class="form-group">
              <label>参数配置:</label>

              <!-- 切换可见性参数 -->
              <template v-if="nodeData.consumerType === 'toggle'">
                <div class="param-group">
                  <label>网格名称:</label>
                  <input
                    type="text"
                    v-model="nodeData.parameters.meshName"
                    @blur="updateNodeData"
                    placeholder="{{meshName}}"
                  />
                </div>
                <div class="param-group">
                  <label>是否可见:</label>
                  <input
                    type="text"
                    v-model="nodeData.parameters.isVisible"
                    @blur="updateNodeData"
                    placeholder="{{meshValue}}"
                  />
                </div>
              </template>

              <!-- 动画控制参数 -->
              <template v-else-if="nodeData.consumerType === 'animate'">
                <div class="param-group">
                  <label>网格名称:</label>
                  <input
                    type="text"
                    v-model="nodeData.parameters.meshName"
                    @blur="updateNodeData"
                    placeholder="{{meshName}}"
                  />
                </div>
                <div class="param-group">
                  <label>动画速度:</label>
                  <input
                    type="text"
                    v-model="nodeData.parameters.speed"
                    @blur="updateNodeData"
                    placeholder="{{meshValue}}"
                  />
                </div>
                <div class="param-group">
                  <label>循环播放:</label>
                  <input
                    type="checkbox"
                    v-model="nodeData.parameters.loop"
                    @change="updateNodeData"
                  />
                </div>
              </template>

              <!-- 属性控制参数 -->
              <template v-else-if="nodeData.consumerType === 'property'">
                <div class="param-group">
                  <label>网格名称:</label>
                  <input
                    type="text"
                    v-model="nodeData.parameters.meshName"
                    @blur="updateNodeData"
                    placeholder="{{meshName}}"
                  />
                </div>
                <div class="param-group">
                  <label>属性名称:</label>
                  <select
                    v-model="nodeData.parameters.property"
                    @change="updateNodeData"
                  >
                    <option value="emissiveColor">发光颜色</option>
                    <option value="diffuseColor">漫反射颜色</option>
                    <option value="alpha">透明度</option>
                    <option value="metallic">金属度</option>
                    <option value="roughness">粗糙度</option>
                  </select>
                </div>
                <div class="param-group">
                  <label>属性值:</label>
                  <input
                    type="text"
                    v-model="nodeData.parameters.value"
                    @blur="updateNodeData"
                    placeholder="{{meshValue}}"
                  />
                </div>
              </template>
            </div>

            <div class="form-group">
              <label>触发条件:</label>
              <select
                v-model="nodeData.triggerCondition"
                @change="updateNodeData"
              >
                <option value="onChange">数据变化时</option>
                <option value="always">始终执行</option>
                <option value="onTrue">数据为真时</option>
                <option value="onFalse">数据为假时</option>
              </select>
            </div>

            <div class="form-group">
              <label>描述:</label>
              <input
                type="text"
                v-model="nodeData.description"
                @blur="updateNodeData"
                placeholder="数据消费配置"
              />
            </div>
          </div>

          <div v-else-if="nodeType === 'lifecycle'" class="form-section">
            <h4>生命周期设置</h4>
            <div class="form-group">
              <label>生命周期类型:</label>
              <select v-model="nodeData.lifecycleType" @change="updateNodeData">
                <option value="onActivated">场景激活 (OnActivated)</option>
                <option value="onDeactivated">场景失活 (OnDeactivated)</option>
                <option value="onDestroyed">场景销毁 (OnDestroyed)</option>
              </select>
            </div>
            <div class="form-group">
              <label>触发时机:</label>
              <select v-model="nodeData.trigger" @change="updateNodeData">
                <option value="immediate">立即执行 (Immediate)</option>
                <option value="onChange">数据变化时 (OnChange)</option>
                <option value="onTrue">数据为真时 (OnTrue)</option>
                <option value="onFalse">数据为假时 (OnFalse)</option>
              </select>
            </div>
            <div class="form-group">
              <label>描述:</label>
              <input
                type="text"
                v-model="nodeData.description"
                @blur="updateNodeData"
                placeholder="生命周期描述"
              />
            </div>
          </div>
        </div>
      </template>
    </div>
    <div v-else class="no-selection">未选中节点</div>
  </div>
</template>

<style scoped>
.properties-panel {
  height: 100%;
  padding: 1rem;
  background-color: #1e1e1e;
  color: #e0e0e0;
  overflow-y: auto;
}

h3 {
  margin-top: 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #444;
}

.panel-content {
  padding-top: 1rem;
}

.node-id {
  font-size: 0.8rem;
  color: #888;
  margin-bottom: 1rem;
}

.form-section {
  margin-bottom: 1.5rem;
}

.form-section h4 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: #aaa;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.3rem;
  font-size: 0.9rem;
  color: #bbb;
}

.param-group {
  margin-left: 1rem;
  margin-bottom: 0.5rem;
}

.param-group label {
  font-size: 0.8rem;
}

input,
select {
  width: 100%;
  padding: 0.5rem;
  background-color: #2a2a2a;
  border: 1px solid #444;
  color: #e0e0e0;
  border-radius: 3px;
}

input:focus,
select:focus {
  outline: none;
  border-color: #0078d7;
}

.color-inputs {
  display: flex;
  gap: 0.5rem;
}

.color-inputs input {
  width: 33%;
}

.code-editor {
  width: 100%;
  background-color: #2a2a2a;
  border: 1px solid #444;
  border-radius: 3px;
  padding: 0.5rem;
  color: #e0e0e0;
  font-family: monospace;
  font-size: 0.8rem;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
}

.code-editor textarea {
  width: 100%;
  height: 100px; /* Adjust height as needed */
  resize: vertical;
  border: none;
  background-color: transparent;
  color: #e0e0e0;
  font-family: monospace;
  font-size: 0.8rem;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
}

.no-selection {
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: center;
  color: #666;
  font-style: italic;
}

.array-item {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.add-btn,
.remove-btn {
  padding: 0.25rem 0.5rem;
  background-color: #333;
  border: 1px solid #555;
  color: #e0e0e0;
  border-radius: 3px;
  cursor: pointer;
}

.add-btn:hover,
.remove-btn:hover {
  background-color: #444;
}

.remove-btn {
  background-color: #5a2a2a;
}

.remove-btn:hover {
  background-color: #7a3a3a;
}

.range-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
}

.range-inputs input {
  width: 80px;
  padding: 4px 8px;
  border: 1px solid #444;
  border-radius: 3px;
  background-color: #333;
  color: #e0e0e0;
}

.match-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.match-group {
  display: flex;
  flex-direction: column;
}

.file-preview {
  margin-top: 1rem;
}

.file-preview-content {
  background-color: #2a2a2a;
  border: 1px solid #444;
  border-radius: 3px;
  width: 100%;
  overflow-x: auto;
  font-family: monospace;
  font-size: 0.8rem;
}

.file-preview-row {
  display: flex;
  border-bottom: 1px solid #444;
}

.file-preview-row:last-child {
  border-bottom: none;
}

.file-preview-row.header {
  background-color: #333;
  font-weight: bold;
}

.file-preview-cell {
  padding: 0.25rem 0.5rem;
  min-width: 100px;
  border-right: 1px solid #444;
  white-space: nowrap;
}

.file-preview-cell:last-child {
  border-right: none;
}

.file-preview-note {
  font-size: 0.75rem;
  color: #888;
  margin-top: 0.25rem;
  font-style: italic;
}
</style>
