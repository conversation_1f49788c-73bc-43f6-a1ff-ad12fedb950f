<script setup lang="ts">
import { Graph, Node } from "@antv/x6";
import { MiniMap } from "@antv/x6-plugin-minimap";
import { inject, onMounted, onUnmounted, ref, Ref, watch } from "vue";
import { compileGraph } from "../compiler/graph-compiler";
import { sceneService } from "../services/SceneService";

// 创建一个简化的节点数据接口
interface NodeData {
  nodeType:
    | "event"
    | "action-highlight"
    | "action-callback"
    | "logic-branch"
    | "logic-sequence"
    | "scene-config";
  displayName?: string; // 显示名称，默认使用中文
  [key: string]: any;
}

// 节点类型映射表 - 用于默认显示名称
const nodeTypeNameMap: Record<string, string> = {
  // 事件类型
  "event-doubleClick": "双击事件",
  "event-click": "单击事件",
  "event-hover": "悬停事件",
  "event-rightDoubleClick": "右键双击事件",
  "event-hotkey": "按键事件",

  // 动作类型
  "action-highlight": "高亮",
  "action-callback-CameraService.focusToDevice": "相机聚焦",
  "action-callback-CameraService.moveCamera": "相机移动",
  "action-callback-AnimationService.playMeshAnimation": "播放动画",
  "action-callback-UIService.showMessage": "显示消息",

  // 逻辑类型
  "logic-branch": "条件分支",
  "logic-sequence": "顺序执行",

  // 场景配置
  "scene-config": "场景配置",
};

// 获取节点显示名称
function getNodeDisplayName(nodeType: string, subType?: string): string {
  if (subType) {
    const key = `${nodeType}-${subType}`;
    return nodeTypeNameMap[key] || subType;
  }
  return nodeTypeNameMap[nodeType] || nodeType;
}

const container = ref<HTMLElement | null>(null);
const graphInstance = ref<Graph | null>(null);
const contextMenu = ref<HTMLElement | null>(null);
const contextMenuPosition = ref({ x: 0, y: 0 });
const contextMenuType = ref<"blank" | "node" | null>(null);
const contextMenuNodeId = ref<string | null>(null);

// 获取当前场景ID
const currentSceneId = inject<Ref<string | null>>("currentSceneId");

const emit = defineEmits(["config-generated", "node-selected"]);

// 监听当前场景变化
watch(
  () => currentSceneId?.value,
  (newSceneId) => {
    if (newSceneId && graphInstance.value) {
      // 检查场景配置节点是否存在
      const sceneNodes = findSceneConfigNodes();
      if (sceneNodes.length === 0) {
        // 如果不存在场景配置节点，创建一个新的
        createSceneConfigNodeFromCurrentScene();
      } else {
        // 如果已存在场景配置节点，更新它
        updateSceneConfigNodesFromCurrentScene(sceneNodes);
      }
    }
  },
  { immediate: false }
);

// 查找场景配置节点
function findSceneConfigNodes() {
  if (!graphInstance.value) return [];

  return graphInstance.value.getNodes().filter((node) => {
    const data = node.getData() as NodeData;
    return data && data.nodeType === "scene-config";
  });
}

// 从当前场景创建场景配置节点
function createSceneConfigNodeFromCurrentScene() {
  if (!graphInstance.value || !currentSceneId?.value) return;

  const scene = sceneService.getCurrentScene();
  if (!scene) return;

  createSceneConfigNode(50, 50, {
    nodeType: "scene-config",
    sceneId: scene.id,
    sceneName: scene.name,
    models: ["陆地开关站/陆地开关站楼-三层-GIS室.glb"], // 默认模型，可以根据需要更改
    environment: "techStyle",
    description: `场景配置节点: ${scene.name}`,
  });
}

// 更新场景配置节点
function updateSceneConfigNodesFromCurrentScene(sceneNodes: Node[]) {
  if (!currentSceneId?.value) return;

  const scene = sceneService.getCurrentScene();
  if (!scene) return;

  // 更新所有场景配置节点
  sceneNodes.forEach((node) => {
    const oldData = node.getData();
    const displayName = `场景: ${scene.name}`;

    node.setData({
      ...oldData,
      sceneId: scene.id,
      sceneName: scene.name,
      displayName,
      description: `场景配置节点: ${scene.name}`,
    });

    // 更新节点标签
    node.setAttrByPath("label/text", displayName);
  });
}

// 生成唯一ID
function generateId(prefix: string): string {
  return `${prefix}-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
}

// 处理节点数据更新事件的处理函数
function handleNodeDataUpdate(event: CustomEvent) {
  if (!graphInstance.value) return;

  const { nodeId, data } = event.detail;
  const node = graphInstance.value.getCellById(nodeId);

  if (node) {
    // 更新节点数据
    node.setData(data);

    // 更新节点标签为中文显示名称
    if (data.displayName) {
      node.setAttrByPath("label/text", data.displayName);
    } else if (data.nodeType === "action-callback" && data.callback) {
      const displayName = getNodeDisplayName("action-callback", data.callback);
      node.setAttrByPath("label/text", displayName);

      // 更新data中的displayName
      const updatedData = { ...data, displayName };
      node.setData(updatedData);
    } else if (data.nodeType) {
      // 为其他节点类型设置默认显示名称
      const displayName = getNodeDisplayName(data.nodeType, data.actionType);
      node.setAttrByPath("label/text", displayName);

      // 更新data中的displayName
      const updatedData = { ...data, displayName };
      node.setData(updatedData);
    }
  }
}

// 处理添加节点请求
function handleAddNodeRequest(event: CustomEvent) {
  if (!graphInstance.value) return;

  const { type, subtype } = event.detail;

  // 根据节点类型创建不同的节点
  switch (type) {
    case "event":
      createEventNode(subtype);
      break;
    case "action-highlight":
      createHighlightNode();
      break;
    case "action-callback":
      createCallbackNode(subtype);
      break;
    case "logic-branch":
      createBranchNode();
      break;
    case "logic-sequence":
      createSequenceNode();
      break;
    case "scene-config":
      createSceneConfigNode();
      break;
  }
}

// 显示右键菜单
function showContextMenu(
  e: MouseEvent,
  type: "blank" | "node",
  nodeId?: string
) {
  e.preventDefault();

  // 设置菜单位置
  contextMenuPosition.value = { x: e.clientX, y: e.clientY };
  contextMenuType.value = type;
  contextMenuNodeId.value = nodeId || null;

  // 显示菜单
  if (contextMenu.value) {
    contextMenu.value.style.display = "block";
    contextMenu.value.style.left = `${e.clientX}px`;
    contextMenu.value.style.top = `${e.clientY}px`;
  }

  // 添加一次性点击事件监听器，点击其他地方时隐藏菜单
  document.addEventListener("click", hideContextMenu, { once: true });
}

// 隐藏右键菜单
function hideContextMenu() {
  if (contextMenu.value) {
    contextMenu.value.style.display = "none";
  }
  contextMenuType.value = null;
  contextMenuNodeId.value = null;
}

// 处理菜单项点击
function handleMenuItemClick(action: string) {
  if (!graphInstance.value) return;

  switch (action) {
    case "add-event":
      createEventNode(
        "doubleClick",
        contextMenuPosition.value.x,
        contextMenuPosition.value.y
      );
      break;
    case "add-highlight":
      createHighlightNode(
        contextMenuPosition.value.x,
        contextMenuPosition.value.y
      );
      break;
    case "add-callback":
      createCallbackNode(
        "CameraService.focusToDevice",
        contextMenuPosition.value.x,
        contextMenuPosition.value.y
      );
      break;
    case "add-branch":
      createBranchNode(
        contextMenuPosition.value.x,
        contextMenuPosition.value.y
      );
      break;
    case "add-scene-config":
      createSceneConfigNode(
        contextMenuPosition.value.x,
        contextMenuPosition.value.y
      );
      break;
    case "delete-node":
      if (contextMenuNodeId.value) {
        graphInstance.value.removeCell(contextMenuNodeId.value);
      }
      break;
    case "copy-node":
      if (contextMenuNodeId.value) {
        const node = graphInstance.value.getCellById(contextMenuNodeId.value);
        if (node) {
          const nodeData = node.getData();
          const nodeType = nodeData.nodeType;

          // 获取节点位置
          const position = node.getBBox().getCenter();
          const x = position.x + 20;
          const y = position.y + 20;

          // 根据节点类型复制节点
          switch (nodeType) {
            case "event":
              createEventNode(nodeData.actionType, x, y);
              break;
            case "action-highlight":
              createHighlightNode(x, y, nodeData);
              break;
            case "action-callback":
              createCallbackNode(nodeData.callback, x, y, nodeData.parameters);
              break;
            case "logic-branch":
              createBranchNode(x, y, nodeData);
              break;
            case "scene-config":
              createSceneConfigNode(x, y, nodeData);
              break;
          }
        }
      }
      break;
  }

  hideContextMenu();
}

// 创建事件节点
function createEventNode(
  eventType: string = "doubleClick",
  x: number = 100,
  y: number = 100
) {
  if (!graphInstance.value) return;

  const graph = graphInstance.value;
  const id = generateId("event");

  // 设置节点显示名称
  const displayName = getNodeDisplayName("event", eventType);
  let description = "事件触发";

  switch (eventType) {
    case "click":
      description = "单击触发";
      break;
    case "hover":
      description = "悬停触发";
      break;
    case "rightDoubleClick":
      description = "右键双击触发";
      break;
    case "hotkey":
      description = "按键触发";
      break;
    case "doubleClick":
    default:
      description = "双击触发";
      break;
  }

  // 创建节点
  const node = graph.addNode({
    id,
    x,
    y,
    shape: "event-node",
    label: displayName,
    data: {
      nodeType: "event",
      actionType: eventType,
      displayName,
      meshNames: { $ref: "templates.1.meshes.buildingLevels" },
      description,
    },
    ports: {
      groups: {
        out: {
          position: "right",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [{ id: "out-exec", group: "out" }],
    },
  });

  return node;
}

// 创建高亮节点
function createHighlightNode(
  x: number = 350,
  y: number = 100,
  data: any = null
) {
  if (!graphInstance.value) return;

  const graph = graphInstance.value;
  const id = generateId("highlight");

  const displayName =
    data?.displayName || getNodeDisplayName("action-highlight");

  const nodeData = data || {
    nodeType: "action-highlight",
    displayName,
    color: [1, 1, 0],
    duration: 1500,
  };

  const node = graph.addNode({
    id,
    x,
    y,
    shape: "action-node",
    label: displayName,
    data: nodeData,
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
        out: {
          position: "right",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [
        { id: "in-exec", group: "in" },
        { id: "out-exec", group: "out" },
      ],
    },
  });

  return node;
}

// 创建回调节点
function createCallbackNode(
  callbackType: string = "CameraService.focusToDevice",
  x: number = 600,
  y: number = 100,
  customParams: any = null
) {
  if (!graphInstance.value) return;

  const graph = graphInstance.value;
  const id = generateId("callback");

  // 设置节点显示名称
  const displayName =
    customParams?.displayName ||
    getNodeDisplayName("action-callback", callbackType);

  // 根据回调类型设置不同的参数
  let parameters: any = customParams || {};

  if (!customParams) {
    switch (callbackType) {
      case "CameraService.focusToDevice":
        parameters = {
          deviceName: "{{meshName}}",
          duration: 120,
          paddingFactor: 1,
        };
        break;
      case "CameraService.moveCamera":
        parameters = {
          position: [0, 0, 0],
          target: [0, 0, 0],
          duration: 120,
        };
        break;
      case "AnimationService.playMeshAnimation":
        parameters = {
          meshName: "{{meshName}}",
          speed: 1,
        };
        break;
      case "UIService.showMessage":
        parameters = {
          message: "操作成功",
          duration: 2000,
          position: "top-center",
        };
        break;
    }
  }

  const node = graph.addNode({
    id,
    x,
    y,
    shape: "action-node",
    label: displayName,
    data: {
      nodeType: "action-callback",
      callback: callbackType,
      displayName,
      parameters,
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [{ id: "in-exec", group: "in" }],
    },
  });

  return node;
}

// 创建分支节点
function createBranchNode(x: number = 350, y: number = 200, data: any = null) {
  if (!graphInstance.value) return;

  const graph = graphInstance.value;
  const id = generateId("branch");

  const displayName = data?.displayName || getNodeDisplayName("logic-branch");

  const nodeData = data || {
    nodeType: "logic-branch",
    displayName,
    conditionProperty: "meshName",
    conditionOperator: "==",
    conditionValue: "GIS室",
    description: "条件分支判断",
  };

  const node = graph.addNode({
    id,
    x,
    y,
    shape: "branch-node",
    label: displayName,
    data: nodeData,
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
        outTrue: {
          position: "right",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#0f0", stroke: "#31d0c6" },
          },
        },
        outFalse: {
          position: "bottom",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#f00", stroke: "#31d0c6" },
          },
        },
      },
      items: [
        { id: "in-exec", group: "in" },
        { id: "true", group: "outTrue", port: "true" },
        { id: "false", group: "outFalse", port: "false" },
      ],
    },
  });

  return node;
}

// 创建顺序节点
function createSequenceNode(x: number = 350, y: number = 300) {
  if (!graphInstance.value) return;

  const graph = graphInstance.value;
  const id = generateId("sequence");

  const displayName = getNodeDisplayName("logic-sequence");

  const node = graph.addNode({
    id,
    x,
    y,
    shape: "action-node",
    label: displayName,
    data: {
      nodeType: "logic-sequence",
      displayName,
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
        out1: {
          position: "right",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
        out2: {
          position: "bottom",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [
        { id: "in-exec", group: "in" },
        { id: "out-exec-1", group: "out1" },
        { id: "out-exec-2", group: "out2" },
      ],
    },
  });

  return node;
}

// 创建场景配置节点
function createSceneConfigNode(
  x: number = 100,
  y: number = 50,
  data: any = null
) {
  if (!graphInstance.value) return;

  const graph = graphInstance.value;
  const id = generateId("scene-config");

  const scene = sceneService.getCurrentScene();
  const sceneName = scene ? scene.name : "默认场景";

  const displayName = data?.displayName || `场景: ${sceneName}`;

  const nodeData = data || {
    nodeType: "scene-config",
    sceneId: scene ? scene.id : "default",
    sceneName: sceneName,
    displayName,
    models: ["陆地开关站/陆地开关站楼-三层-GIS室.glb"],
    environment: "techStyle",
    description: `场景配置节点: ${sceneName}`,
  };

  const node = graph.addNode({
    id,
    x,
    y,
    shape: "scene-config-node",
    label: displayName,
    data: nodeData,
  });

  return node;
}

// 保存画布数据为JSON格式
function saveGraphData() {
  if (graphInstance.value) {
    return graphInstance.value.toJSON();
  }
  return null;
}

// 从JSON加载画布数据
function loadGraphData(graphData: any) {
  if (!graphInstance.value || !graphData) return;

  // 清空当前画布
  clearGraph();

  // 加载新数据
  graphInstance.value.fromJSON(graphData);
}

// 清空画布
function clearGraph() {
  if (!graphInstance.value) return;
  graphInstance.value.clearCells();
}

// 监听场景创建事件
function handleSceneCreated(event: CustomEvent) {
  const { sceneId } = event.detail;
  if (sceneId && graphInstance.value) {
    // 检查是否有场景配置节点
    const sceneNodes = findSceneConfigNodes();
    if (sceneNodes.length === 0) {
      // 如果不存在，创建新的场景配置节点
      createSceneConfigNodeFromCurrentScene();
    } else {
      // 如果存在，更新场景配置节点
      updateSceneConfigNodesFromCurrentScene(sceneNodes);
    }
  }
}

onMounted(() => {
  if (container.value) {
    // 延迟初始化，确保DOM完全渲染
    setTimeout(() => {
      initGraph();
      window.addEventListener("resize", resizeGraph);
    }, 100);
  }

  // 添加全局事件监听器
  document.addEventListener(
    "update-node-data",
    handleNodeDataUpdate as EventListener
  );
  document.addEventListener(
    "add-node-request",
    handleAddNodeRequest as EventListener
  );
  document.addEventListener(
    "scene-created",
    handleSceneCreated as EventListener
  );
});

onUnmounted(() => {
  // 移除全局事件监听器
  document.removeEventListener(
    "update-node-data",
    handleNodeDataUpdate as EventListener
  );
  document.removeEventListener(
    "add-node-request",
    handleAddNodeRequest as EventListener
  );
  document.removeEventListener(
    "scene-created",
    handleSceneCreated as EventListener
  );

  // 移除resize监听器
  window.removeEventListener("resize", resizeGraph);
});

// 定义公开方法
defineExpose({
  saveGraphData,
  loadGraphData,
  clearGraph,
  changeNodeType,
  generate,
});

// 暴露的节点类型变更方法
function changeNodeType(
  nodeId: string,
  oldType: string,
  newType: string,
  newData: any
) {
  if (!graphInstance.value) return;

  const node = graphInstance.value.getCellById(nodeId);
  if (!node || !node.isNode()) return;

  // 获取原始节点的位置
  const position = node.getBBox();
  const x = position.x;
  const y = position.y;

  // 创建新节点
  let newNode;
  switch (newType) {
    case "event":
      newNode = createEventNode(newData.actionType || "doubleClick", x, y);
      break;
    case "action-highlight":
      newNode = createHighlightNode(x, y, newData);
      break;
    case "action-callback":
      // 确保处理参数为空的情况
      const parameters = newData.parameters || {};
      newNode = createCallbackNode(
        newData.callback || "CameraService.focusToDevice",
        x,
        y,
        parameters
      );
      break;
    case "logic-branch":
      newNode = createBranchNode(x, y, newData);
      break;
    case "scene-config":
      newNode = createSceneConfigNode(x, y, newData);
      break;
    default:
      console.error(`未知节点类型: ${newType}`);
      return;
  }

  if (!newNode) return;

  // 删除原节点
  graphInstance.value.removeCell(node);

  // 重新发送选中事件
  emit("node-selected", {
    id: newNode.id,
    data: newNode.getData(),
  });
}

// 调整画布大小
function resizeGraph() {
  if (graphInstance.value && container.value) {
    const { offsetWidth, offsetHeight } = container.value;
    graphInstance.value.resize(offsetWidth, offsetHeight);
    graphInstance.value.centerContent();
  }
}

// 初始化图形
function initGraph() {
  if (!container.value) return;

  const graph = new Graph({
    container: container.value,
    width: container.value.offsetWidth || 800,
    height: container.value.offsetHeight || 600,
    grid: {
      visible: true,
      type: "dot",
      size: 10,
    },
    panning: {
      enabled: true,
    },
    mousewheel: {
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: "ctrl",
      minScale: 0.5,
      maxScale: 2,
    },
    connecting: {
      allowBlank: false,
      allowNode: false,
      allowLoop: false,
      snap: true,
      createEdge() {
        return this.createEdge({
          shape: "edge",
          attrs: {
            line: {
              stroke: "#8f8f8f",
              strokeWidth: 1,
              targetMarker: {
                name: "block",
                size: 6,
              },
            },
          },
        });
      },
      validateConnection({
        sourceView,
        targetView,
        sourceMagnet,
        targetMagnet,
      }) {
        if (!sourceMagnet || !targetMagnet) {
          return false;
        }

        // 不允许连接到同一个节点
        if (sourceView === targetView) {
          return false;
        }

        return true;
      },
    },
    highlighting: {
      magnetAvailable: {
        name: "stroke",
        args: {
          attrs: {
            fill: "#5F95FF",
            stroke: "#5F95FF",
          },
        },
      },
    },
  });

  graphInstance.value = graph;

  // 为自动调整视图添加延时
  setTimeout(() => {
    graph.zoomToFit({ padding: 20 });
    initMinimap(graph);
  }, 200);

  // 自定义节点样式
  Graph.registerNode("event-node", {
    inherit: "rect",
    width: 180,
    height: 40,
    attrs: {
      body: {
        fill: "#4a6b8a",
        stroke: "#5F95FF",
        strokeWidth: 1,
        rx: 6,
        ry: 6,
      },
      label: {
        fill: "#ffffff",
        fontSize: 14,
        fontWeight: "bold",
        refX: 0.5,
        refY: 0.5,
        textAnchor: "middle",
        textVerticalAnchor: "middle",
      },
    },
    ports: {
      groups: {
        out: {
          position: "right",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
      },
    },
  });

  Graph.registerNode("action-node", {
    inherit: "rect",
    width: 220,
    height: 40,
    attrs: {
      body: {
        fill: "#4a8a6b",
        stroke: "#5F95FF",
        strokeWidth: 1,
        rx: 6,
        ry: 6,
      },
      label: {
        fill: "#ffffff",
        fontSize: 14,
        fontWeight: "bold",
        refX: 0.5,
        refY: 0.5,
        textAnchor: "middle",
        textVerticalAnchor: "middle",
      },
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
        out: {
          position: "right",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
      },
    },
  });

  Graph.registerNode("branch-node", {
    inherit: "rect",
    width: 220,
    height: 60,
    attrs: {
      body: {
        fill: "#8a4a6b",
        stroke: "#5F95FF",
        strokeWidth: 1,
        rx: 6,
        ry: 6,
      },
      label: {
        fill: "#ffffff",
        fontSize: 14,
        fontWeight: "bold",
        refX: 0.5,
        refY: 0.5,
        textAnchor: "middle",
        textVerticalAnchor: "middle",
      },
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#fff",
              stroke: "#5F95FF",
            },
          },
        },
        outTrue: {
          position: "right",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#0f0",
              stroke: "#5F95FF",
            },
          },
        },
        outFalse: {
          position: "bottom",
          attrs: {
            circle: {
              magnet: true,
              r: 4,
              fill: "#f00",
              stroke: "#5F95FF",
            },
          },
        },
      },
    },
  });

  Graph.registerNode("scene-config-node", {
    inherit: "rect",
    width: 280,
    height: 60,
    attrs: {
      body: {
        fill: "#1a3f5c",
        stroke: "#5b8fb9",
        strokeWidth: 2,
        rx: 6,
        ry: 6,
      },
      label: {
        fill: "#ffffff",
        fontSize: 16,
        fontWeight: "bold",
        refX: 0.5,
        refY: 0.5,
        textAnchor: "middle",
        textVerticalAnchor: "middle",
      },
    },
  });

  // --- 创建示例节点 ---

  // 不再自动创建场景配置节点，而是通过场景管理自动创建
  // 延时创建场景配置节点，确保场景数据加载完成
  setTimeout(() => {
    if (currentSceneId?.value) {
      const sceneNodes = findSceneConfigNodes();
      if (sceneNodes.length === 0) {
        createSceneConfigNodeFromCurrentScene();
      } else {
        updateSceneConfigNodesFromCurrentScene(sceneNodes);
      }
    }
  }, 300);

  // 1. 事件节点 (逻辑起点)
  const eventNode = graph.addNode({
    id: "event-1",
    x: 50,
    y: 150,
    shape: "event-node",
    label: "双击事件",
    data: {
      nodeType: "event",
      actionType: "doubleClick",
      displayName: "双击事件",
      meshNames: { $ref: "templates.1.meshes.buildingLevels" },
      description: "双击触发高亮和相机聚焦",
    },
    ports: {
      groups: {
        out: {
          position: "right",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [{ id: "out-exec", group: "out" }],
    },
  });

  // 2. 分支节点 (条件判断)
  const branchNode = graph.addNode({
    id: "branch-1",
    x: 300,
    y: 150,
    shape: "branch-node",
    label: "条件分支",
    data: {
      nodeType: "logic-branch",
      displayName: "条件分支",
      conditionProperty: "meshName",
      conditionOperator: "==",
      conditionValue: "GIS室",
      description: "判断是否为GIS室",
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
        outTrue: {
          position: "right",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#0f0", stroke: "#31d0c6" },
          },
        },
        outFalse: {
          position: "bottom",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#f00", stroke: "#31d0c6" },
          },
        },
      },
      items: [
        { id: "in-exec", group: "in" },
        { id: "true", group: "outTrue", port: "true" },
        { id: "false", group: "outFalse", port: "false" },
      ],
    },
  });

  // 3. 高亮动作节点 (True分支)
  const highlightNode = graph.addNode({
    id: "action-highlight",
    x: 600,
    y: 100,
    shape: "action-node",
    label: "高亮",
    data: {
      nodeType: "action-highlight",
      displayName: "高亮",
      color: [0, 1, 0], // 绿色高亮
      duration: 1500,
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
        out: {
          position: "right",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [
        { id: "in-exec", group: "in" },
        { id: "out-exec", group: "out" },
      ],
    },
  });

  // 4. 高亮动作节点 (False分支)
  const highlightNode2 = graph.addNode({
    id: "action-highlight-2",
    x: 300,
    y: 300,
    shape: "action-node",
    label: "高亮",
    data: {
      nodeType: "action-highlight",
      displayName: "高亮",
      color: [1, 0, 0], // 红色高亮
      duration: 1500,
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
        out: {
          position: "right",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [
        { id: "in-exec", group: "in" },
        { id: "out-exec", group: "out" },
      ],
    },
  });

  // 5. 回调动作节点 (连接到True分支)
  const callbackNode = graph.addNode({
    id: "action-callback",
    x: 900,
    y: 100,
    shape: "action-node",
    label: "相机聚焦",
    data: {
      nodeType: "action-callback",
      callback: "CameraService.focusToDevice",
      displayName: "相机聚焦",
      parameters: {
        deviceName: "{{meshName}}",
        duration: 120,
        paddingFactor: 1,
      },
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [{ id: "in-exec", group: "in" }],
    },
  });

  // 6. 回调动作节点 (连接到False分支)
  const callbackNode2 = graph.addNode({
    id: "action-callback-2",
    x: 600,
    y: 300,
    shape: "action-node",
    label: "显示消息",
    data: {
      nodeType: "action-callback",
      callback: "UIService.showMessage",
      displayName: "显示消息",
      parameters: {
        message: "不是GIS室",
        duration: 2000,
        position: "top-center",
      },
    },
    ports: {
      groups: {
        in: {
          position: "left",
          attrs: {
            circle: { magnet: true, r: 4, fill: "#fff", stroke: "#31d0c6" },
          },
        },
      },
      items: [{ id: "in-exec", group: "in" }],
    },
  });

  // 连接节点
  graph.addEdge({
    source: { cell: eventNode.id, port: "out-exec" },
    target: { cell: branchNode.id, port: "in-exec" },
  });

  // True分支连接
  graph.addEdge({
    source: { cell: branchNode.id, port: "true" },
    target: { cell: highlightNode.id, port: "in-exec" },
  });
  graph.addEdge({
    source: { cell: highlightNode.id, port: "out-exec" },
    target: { cell: callbackNode.id, port: "in-exec" },
  });

  // False分支连接
  graph.addEdge({
    source: { cell: branchNode.id, port: "false" },
    target: { cell: highlightNode2.id, port: "in-exec" },
  });
  graph.addEdge({
    source: { cell: highlightNode2.id, port: "out-exec" },
    target: { cell: callbackNode2.id, port: "in-exec" },
  });

  // 添加节点选择事件监听
  graph.on("node:click", ({ node }) => {
    emit("node-selected", node);
  });

  // 点击空白处取消选择
  graph.on("blank:click", () => {
    emit("node-selected", null);
  });

  // 添加右键菜单事件监听
  graph.on("blank:contextmenu", ({ e }) => {
    showContextMenu(e as unknown as MouseEvent, "blank");
  });

  graph.on("node:contextmenu", ({ e, node }) => {
    showContextMenu(e as unknown as MouseEvent, "node", node.id);
  });

  // 添加键盘快捷键
  document.addEventListener("keydown", (e) => {
    // 删除：Delete 或 Backspace
    if (e.key === "Delete" || e.key === "Backspace") {
      const selectedNodes = graph
        .getNodes()
        .filter((node) => node.hasTools?.());
      if (selectedNodes.length) {
        e.preventDefault();
        graph.removeCells(selectedNodes);
      }
    }
  });
}

// 初始化小地图
function initMinimap(graph: Graph) {
  const minimapContainer = document.getElementById("minimap-container");
  if (!minimapContainer) {
    console.warn("未找到小地图容器元素");
    return;
  }

  try {
    graph.use(
      new MiniMap({
        container: minimapContainer,
        width: minimapContainer.clientWidth || 200,
        height: minimapContainer.clientHeight || 150,
        padding: 10,
        scalable: true,
        minScale: 0.1,
        maxScale: 0.9,
      })
    );
    console.log("小地图初始化成功");
  } catch (error) {
    console.error("小地图初始化失败:", error);
  }
}

// 生成配置
function generate() {
  if (graphInstance.value) {
    try {
      const graphData = graphInstance.value.toJSON();
      const compiledConfig = compileGraph(graphData);
      emit("config-generated", compiledConfig);
    } catch (error) {
      console.error("Error generating config:", error);
    }
  }
}
