<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { compileGraph } from "../compiler/graph-compiler";
import { sceneService } from "../services/SceneService";
import { errorService } from "../services/ErrorService";
import GraphCanvas from "./editor/GraphCanvas.vue";
import EditorToolbar from "./editor/EditorToolbar.vue";
import type { NodeData } from "../types";

const emit = defineEmits<{
  nodeSelected: [node: any]
  configGenerated: [config: any]
  nodeTypeChanged: [data: { nodeId: string; newType: string; oldType: string }]
}>()

// 组件引用
const graphCanvasRef = ref<InstanceType<typeof GraphCanvas>>()
const currentGraph = ref<any>(null)

onMounted(() => {
  setupEventListeners()
})

onUnmounted(() => {
  removeEventListeners()
})

function setupEventListeners() {
  // 监听场景创建事件
  document.addEventListener('scene-created', handleSceneCreated)
}

function removeEventListeners() {
  document.removeEventListener('scene-created', handleSceneCreated)
}

function handleSceneCreated(event: any) {
  const { sceneId } = event.detail
  if (sceneId && graphCanvasRef.value) {
    // 为新场景创建默认的场景配置节点
    createDefaultSceneConfigNode(sceneId)
  }
}

function createDefaultSceneConfigNode(sceneId: string) {
  const scene = sceneService.getScene(sceneId)
  if (!scene) return

  // 通过事件触发添加节点
  const event = new CustomEvent('add-node-request', {
    detail: {
      type: 'scene-config',
      data: {
        sceneId: scene.id,
        sceneName: scene.name,
        models: [],
        environment: 'techStyle'
      }
    }
  })
  document.dispatchEvent(event)
}

function handleNodeSelected(node: any) {
  emit('nodeSelected', node)
}

function handleGraphChanged(graphData: any) {
  // 图形变化时的处理逻辑
}

function handleCompile() {
  try {
    const graphData = graphCanvasRef.value?.saveGraphData()
    if (graphData) {
      const config = compileGraph(graphData)
      emit('configGenerated', config)
    }
  } catch (error) {
    errorService.handleError(error as Error, 'LogicEditor.handleCompile')
  }
}

function handleSave() {
  try {
    const graphData = graphCanvasRef.value?.saveGraphData()
    if (graphData && sceneService.getCurrentSceneId()) {
      sceneService.updateGraphData(sceneService.getCurrentSceneId()!, graphData)
      sceneService.saveToLocalStorage()
    }
  } catch (error) {
    errorService.handleError(error as Error, 'LogicEditor.handleSave')
  }
}

// 暴露方法给父组件
defineExpose({
  saveGraphData: () => graphCanvasRef.value?.saveGraphData(),
  loadGraphData: (data: any) => graphCanvasRef.value?.loadGraphData(data),
  clearGraph: () => graphCanvasRef.value?.clearGraph(),
})






</script>

<template>
  <div class="logic-editor">
    <EditorToolbar
      :graph="currentGraph"
      @compile="handleCompile"
      @save="handleSave"
    />
    <GraphCanvas
      ref="graphCanvasRef"
      @node-selected="handleNodeSelected"
      @graph-changed="handleGraphChanged"
    />
  </div>
</template>

<style scoped>
.logic-editor {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: #1e1e1e;
}
</style>




























