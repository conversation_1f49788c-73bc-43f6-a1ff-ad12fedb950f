<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import { sceneService, type Scene } from "../../services/SceneService";

const props = defineProps<{
  currentSceneId: string | null;
}>();

const emit = defineEmits([
  "scene-selected",
  "scene-created",
  "scene-deleted",
  "scene-renamed",
]);

const scenes = ref<Scene[]>([]);
const newSceneName = ref("");
const editingScene = ref<{ id: string; name: string } | null>(null);
const isImporting = ref(false);

// 加载场景数据
onMounted(() => {
  loadScenes();
});

// 监听场景变化
watch(
  () => sceneService.getAllScenes(),
  () => {
    loadScenes();
  },
  { deep: true }
);

// 加载场景列表
function loadScenes() {
  scenes.value = sceneService.getAllScenes();
}

// 创建新场景
function createScene(name: string = "") {
  const sceneName =
    name || newSceneName.value || `场景 ${scenes.value.length + 1}`;
  const newScene = sceneService.createScene(sceneName);

  newSceneName.value = "";
  sceneService.saveToLocalStorage();

  // 发出场景创建事件
  emit("scene-created", newScene);

  // 自动选择新创建的场景
  selectScene(newScene.id);
}

// 选择场景
function selectScene(sceneId: string) {
  sceneService.setCurrentScene(sceneId);
  sceneService.saveToLocalStorage();
  emit("scene-selected", sceneId);
}

// 删除场景
function deleteScene(sceneId: string, event?: Event) {
  if (event) {
    event.stopPropagation();
  }

  if (scenes.value.length <= 1) {
    alert("至少需要保留一个场景");
    return;
  }

  const confirmDelete = confirm("确定要删除此场景吗？此操作不可撤销。");
  if (confirmDelete) {
    sceneService.deleteScene(sceneId);
    sceneService.saveToLocalStorage();

    // 发出场景删除事件
    emit("scene-deleted", sceneId);
  }
}

// 开始编辑场景名称
function startEditName(scene: Scene, event?: Event) {
  if (event) {
    event.stopPropagation();
  }
  editingScene.value = { id: scene.id, name: scene.name };
}

// 保存场景名称
function saveSceneName() {
  if (editingScene.value) {
    sceneService.updateScene(editingScene.value.id, {
      name: editingScene.value.name,
    });
    sceneService.saveToLocalStorage();
    emit("scene-renamed", editingScene.value.id, editingScene.value.name);
    editingScene.value = null;
  }
}

// 取消编辑场景名称
function cancelEditName() {
  editingScene.value = null;
}

// 从config.js导入场景
async function importFromConfig() {
  const confirmImport = confirm(
    "确定要从config.js导入场景吗？这将覆盖当前所有场景。"
  );
  if (!confirmImport) return;

  isImporting.value = true;
  try {
    await sceneService.resetScenes();
    loadScenes();

    // 发出场景选择事件，通知父组件当前场景已更改
    const currentSceneId = sceneService.getCurrentSceneId();
    if (currentSceneId) {
      emit("scene-selected", currentSceneId);
    }

    alert("成功从config.js导入场景！");
  } catch (error) {
    console.error("导入场景失败", error);
    alert("导入场景失败，请查看控制台获取详细信息。");
  } finally {
    isImporting.value = false;
  }
}

// 格式化日期
function formatDate(timestamp: number): string {
  return new Date(timestamp).toLocaleString();
}

// 暴露方法供父组件调用
defineExpose({
  loadScenes,
  createScene,
  importFromConfig,
});
</script>

<template>
  <div class="scene-manager">
    <h3>场景管理</h3>

    <div class="scene-list">
      <div
        v-for="scene in scenes"
        :key="scene.id"
        class="scene-item"
        :class="{ active: scene.id === currentSceneId }"
        @click="selectScene(scene.id)"
      >
        <!-- 编辑模式 -->
        <div
          v-if="editingScene && editingScene.id === scene.id"
          class="scene-edit"
        >
          <input
            v-model="editingScene.name"
            @keyup.enter="saveSceneName"
            @keyup.esc="cancelEditName"
            autofocus
          />
          <div class="edit-buttons">
            <button @click="saveSceneName" class="btn-save">✓</button>
            <button @click="cancelEditName" class="btn-cancel">✕</button>
          </div>
        </div>

        <!-- 显示模式 -->
        <div v-else class="scene-display">
          <div class="scene-info">
            <span class="scene-name" @dblclick="startEditName(scene, $event)">
              {{ scene.name }}
              <span v-if="scene.configId" class="scene-id"
                >({{ scene.configId }})</span
              >
            </span>
            <span class="scene-date" v-if="scene.lastModified">
              {{ formatDate(scene.lastModified) }}
            </span>
            <span
              v-if="scene.models && scene.models.length"
              class="scene-models"
            >
              模型: {{ scene.models.length }}个
            </span>
          </div>
          <button
            v-if="scenes.length > 1"
            @click.stop="deleteScene(scene.id, $event)"
            class="btn-delete"
          >
            🗑️
          </button>
        </div>
      </div>
    </div>

    <div class="scene-actions">
      <div class="add-scene">
        <input
          v-model="newSceneName"
          placeholder="输入场景名称"
          @keyup.enter="createScene()"
        />
        <button @click="createScene()" class="btn-add">添加场景</button>
      </div>
      <button
        @click="importFromConfig()"
        class="btn-import"
        :disabled="isImporting"
      >
        {{ isImporting ? "导入中..." : "从config.js导入" }}
      </button>
    </div>
  </div>
</template>

<style scoped>
.scene-manager {
  padding: 10px;
  border-bottom: 1px solid #444;
  background-color: #2a2a2a;
}

h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #e0e0e0;
  font-size: 16px;
}

.scene-list {
  margin-bottom: 10px;
  max-height: 200px;
  overflow-y: auto;
}

.scene-item {
  display: flex;
  align-items: center;
  padding: 8px;
  margin-bottom: 4px;
  background-color: #3a3a3a;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.scene-item:hover {
  background-color: #464646;
}

.scene-item.active {
  background-color: #4a6b8a;
}

.scene-display {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
}

.scene-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.scene-name {
  color: #e0e0e0;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.scene-id {
  color: #aaa;
  font-size: 12px;
}

.scene-date {
  color: #999;
  font-size: 12px;
  margin-top: 2px;
}

.scene-models {
  color: #999;
  font-size: 12px;
  margin-top: 2px;
}

.btn-delete {
  opacity: 0.5;
  background: none;
  border: none;
  color: #e0e0e0;
  cursor: pointer;
  padding: 2px 6px;
  font-size: 12px;
}

.btn-delete:hover {
  opacity: 1;
}

.scene-edit {
  display: flex;
  width: 100%;
}

.scene-edit input {
  flex: 1;
  background-color: #2a2a2a;
  border: 1px solid #555;
  color: #e0e0e0;
  padding: 4px 8px;
  border-radius: 3px;
}

.edit-buttons {
  display: flex;
  margin-left: 8px;
}

.btn-save,
.btn-cancel {
  background: none;
  border: none;
  color: #e0e0e0;
  cursor: pointer;
  padding: 0 4px;
}

.btn-save {
  color: #4caf50;
}

.btn-cancel {
  color: #f44336;
}

.scene-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.add-scene {
  display: flex;
}

.add-scene input {
  flex: 1;
  background-color: #2a2a2a;
  border: 1px solid #555;
  color: #e0e0e0;
  padding: 6px 8px;
  border-radius: 3px 0 0 3px;
}

.btn-add {
  background-color: #4a6b8a;
  border: none;
  color: #e0e0e0;
  padding: 6px 12px;
  cursor: pointer;
  border-radius: 0 3px 3px 0;
}

.btn-import {
  background-color: #5a8a6a;
  border: none;
  color: #e0e0e0;
  padding: 8px;
  cursor: pointer;
  border-radius: 3px;
  width: 100%;
}

.btn-import:disabled {
  background-color: #555;
  cursor: not-allowed;
}
</style>
