<template>
  <div class="demo-container">
    <div class="demo-header">
      <h2>DDD-Flow 节点连接系统演示</h2>
      <p>类似 Unreal Engine 蓝图编辑器的节点连接体验</p>
    </div>
    
    <div class="demo-controls">
      <button @click="addEventNode" class="demo-btn event">
        添加事件节点
      </button>
      <button @click="addActionNode" class="demo-btn action">
        添加动作节点
      </button>
      <button @click="addBranchNode" class="demo-btn logic">
        添加分支节点
      </button>
      <button @click="addDataNode" class="demo-btn data">
        添加数据节点
      </button>
      <button @click="clearAll" class="demo-btn clear">
        清空画布
      </button>
    </div>
    
    <div class="demo-info">
      <div class="info-section">
        <h3>端口类型说明</h3>
        <div class="port-legend">
          <div class="port-item">
            <div class="port-icon exec"></div>
            <span>执行流端口（白色）- 控制节点执行顺序</span>
          </div>
          <div class="port-item">
            <div class="port-icon data"></div>
            <span>数据流端口（蓝色）- 传递数据值</span>
          </div>
          <div class="port-item">
            <div class="port-icon true"></div>
            <span>True输出（绿色）- 条件为真时执行</span>
          </div>
          <div class="port-item">
            <div class="port-icon false"></div>
            <span>False输出（红色）- 条件为假时执行</span>
          </div>
        </div>
      </div>
      
      <div class="info-section">
        <h3>操作说明</h3>
        <ul>
          <li>鼠标悬停在端口上查看类型标签</li>
          <li>从输出端口拖拽到输入端口创建连接</li>
          <li>执行流只能连接到执行流</li>
          <li>数据流只能连接到数据流</li>
          <li>连接线会根据类型显示不同颜色</li>
          <li>悬停连接线查看高亮效果</li>
        </ul>
      </div>
    </div>
    
    <div class="demo-canvas">
      <GraphCanvas 
        ref="graphCanvas"
        @nodeSelected="handleNodeSelected"
      />
    </div>
    
    <div v-if="selectedNode" class="demo-selected">
      <h3>选中节点信息</h3>
      <pre>{{ JSON.stringify(selectedNode, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import GraphCanvas from '../editor/GraphCanvas.vue'

const graphCanvas = ref<InstanceType<typeof GraphCanvas> | null>(null)
const selectedNode = ref<any>(null)

function handleNodeSelected(node: any) {
  selectedNode.value = node
}

function addEventNode() {
  if (graphCanvas.value) {
    // 触发添加节点事件
    const event = new CustomEvent('add-node-request', {
      detail: { type: 'event', subtype: 'click' }
    })
    document.dispatchEvent(event)
  }
}

function addActionNode() {
  if (graphCanvas.value) {
    const event = new CustomEvent('add-node-request', {
      detail: { type: 'action-highlight' }
    })
    document.dispatchEvent(event)
  }
}

function addBranchNode() {
  if (graphCanvas.value) {
    const event = new CustomEvent('add-node-request', {
      detail: { type: 'logic-branch' }
    })
    document.dispatchEvent(event)
  }
}

function addDataNode() {
  if (graphCanvas.value) {
    const event = new CustomEvent('add-node-request', {
      detail: { type: 'data-source', subtype: 'polling' }
    })
    document.dispatchEvent(event)
  }
}

function clearAll() {
  if (graphCanvas.value) {
    graphCanvas.value.clearGraph()
    selectedNode.value = null
  }
}
</script>

<style scoped>
.demo-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #1a1a1a;
  color: #e0e0e0;
}

.demo-header {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #333;
}

.demo-header h2 {
  margin: 0 0 10px 0;
  color: #ffffff;
}

.demo-header p {
  margin: 0;
  color: #999;
}

.demo-controls {
  display: flex;
  gap: 12px;
  padding: 16px;
  border-bottom: 1px solid #333;
  flex-wrap: wrap;
}

.demo-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.2s;
}

.demo-btn.event {
  background: #4CAF50;
  color: white;
}

.demo-btn.action {
  background: #FF9800;
  color: white;
}

.demo-btn.logic {
  background: #9C27B0;
  color: white;
}

.demo-btn.data {
  background: #2196F3;
  color: white;
}

.demo-btn.clear {
  background: #F44336;
  color: white;
}

.demo-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.demo-info {
  display: flex;
  gap: 20px;
  padding: 16px;
  border-bottom: 1px solid #333;
  background: #2a2a2a;
}

.info-section {
  flex: 1;
}

.info-section h3 {
  margin: 0 0 12px 0;
  color: #ffffff;
  font-size: 14px;
}

.port-legend {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.port-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.port-icon {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid;
}

.port-icon.exec {
  background: #404040;
  border-color: #ffffff;
}

.port-icon.data {
  background: #0050b3;
  border-color: #1890ff;
}

.port-icon.true {
  background: #1B5E20;
  border-color: #4CAF50;
}

.port-icon.false {
  background: #B71C1C;
  border-color: #F44336;
}

.info-section ul {
  margin: 0;
  padding-left: 16px;
  font-size: 12px;
  line-height: 1.5;
}

.demo-canvas {
  flex: 1;
  min-height: 400px;
  border: 1px solid #333;
  margin: 0 16px 16px 16px;
  border-radius: 4px;
  overflow: hidden;
}

.demo-selected {
  margin: 0 16px 16px 16px;
  padding: 12px;
  background: #2a2a2a;
  border-radius: 4px;
  border: 1px solid #333;
}

.demo-selected h3 {
  margin: 0 0 8px 0;
  color: #ffffff;
  font-size: 14px;
}

.demo-selected pre {
  margin: 0;
  font-size: 11px;
  color: #999;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
