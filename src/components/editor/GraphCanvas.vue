<template>
  <div class="graph-canvas">
    <div id="graph-container" ref="graphContainer"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, defineExpose } from 'vue'
import { Graph, Node } from '@antv/x6'
import { MiniMap } from '@antv/x6-plugin-minimap'
import type { NodeData } from '../../types'
import { errorService } from '../../services/ErrorService'
import { performanceService } from '../../services/PerformanceService'
import { cacheService } from '../../services/CacheService'
import { undoRedoService, FunctionCommand } from '../../services/UndoRedoService'
import { keyboardService, registerShortcuts } from '../../services/KeyboardService'

const emit = defineEmits<{
  nodeSelected: [node: any]
  graphChanged: [graphData: any]
}>()

const graphContainer = ref<HTMLElement>()
let graph: Graph | null = null
let shortcutIds: string[] = []

onMounted(() => {
  performanceService.startMeasure('GraphCanvas.mount')
  initializeGraph()
  setupEventListeners()
  setupKeyboardShortcuts()
  performanceService.endMeasure('GraphCanvas.mount')
})

onUnmounted(() => {
  // 清理资源
  if (graph) {
    graph.dispose()
  }

  // 注销快捷键
  shortcutIds.forEach(id => keyboardService.unregister(id))

  // 清理缓存
  cacheService.clear()
})

function setupKeyboardShortcuts() {
  if (!graph) return

  shortcutIds = registerShortcuts([
    {
      keys: ['ctrl', 'z'],
      description: '撤销',
      handler: () => undoRedoService.undo(),
      context: 'graph-editor'
    },
    {
      keys: ['ctrl', 'y'],
      description: '重做',
      handler: () => undoRedoService.redo(),
      context: 'graph-editor'
    },
    {
      keys: ['ctrl', 'a'],
      description: '全选',
      handler: () => graph?.selectAll(),
      context: 'graph-editor'
    },
    {
      keys: ['delete'],
      description: '删除选中项',
      handler: () => deleteSelected(),
      context: 'graph-editor'
    },
    {
      keys: ['ctrl', 'c'],
      description: '复制',
      handler: () => copySelected(),
      context: 'graph-editor'
    },
    {
      keys: ['ctrl', 'v'],
      description: '粘贴',
      handler: () => pasteFromClipboard(),
      context: 'graph-editor'
    },
  ])

  // 设置上下文
  keyboardService.setContext('graph-editor')
}

function deleteSelected() {
  if (!graph) return

  const selectedCells = graph.getSelectedCells()
  if (selectedCells.length > 0) {
    const command = new FunctionCommand(
      `删除 ${selectedCells.length} 个元素`,
      () => {
        graph!.removeCells(selectedCells)
      },
      () => {
        graph!.addCells(selectedCells)
      }
    )
    undoRedoService.executeCommand(command)
  }
}

function copySelected() {
  if (!graph) return

  const selectedCells = graph.getSelectedCells()
  if (selectedCells.length > 0) {
    graph.copy(selectedCells)
    // 缓存复制的数据
    cacheService.set('clipboard', selectedCells.map(cell => cell.toJSON()), 5 * 60 * 1000)
  }
}

function pasteFromClipboard() {
  if (!graph) return

  const clipboardData = cacheService.get('clipboard')
  if (clipboardData) {
    const command = new FunctionCommand(
      `粘贴 ${clipboardData.length} 个元素`,
      () => {
        graph!.paste({ offset: 20 })
      },
      () => {
        // 撤销粘贴操作需要删除刚粘贴的元素
        const pastedCells = graph!.getSelectedCells()
        graph!.removeCells(pastedCells)
      }
    )
    undoRedoService.executeCommand(command)
  }
}

function initializeGraph() {
  if (!graphContainer.value) return

  try {
    performanceService.startMeasure('GraphCanvas.initializeGraph')

    graph = new Graph({
      container: graphContainer.value,
      width: graphContainer.value.clientWidth,
      height: graphContainer.value.clientHeight,
      background: {
        color: '#1e1e1e',
      },
      grid: {
        visible: true,
        type: 'doubleMesh',
        args: [
          {
            color: '#333',
            thickness: 1,
          },
          {
            color: '#222',
            thickness: 1,
            factor: 4,
          },
        ],
      },
      selecting: {
        enabled: true,
        rubberband: true,
        movable: true,
        showNodeSelectionBox: true,
      },
      connecting: {
        router: 'normal',
        connector: {
          name: 'smooth',
          args: {
            direction: 'H',
          },
        },
        anchor: 'center',
        connectionPoint: 'boundary',
        allowBlank: false,
        allowLoop: false,
        allowNode: false,
        allowEdge: false,
        allowPort: true,
        highlight: true,
        snap: {
          radius: 30,
        },
        validateMagnet({ magnet }) {
          // 只允许连接到端口
          return magnet.getAttribute('port-group') !== null
        },
        validateConnection({ sourceView, targetView, sourceMagnet, targetMagnet }) {
          // 不允许连接到自己
          if (sourceView === targetView) {
            return false
          }

          // 获取端口组信息
          const sourceGroup = sourceMagnet?.getAttribute('port-group')
          const targetGroup = targetMagnet?.getAttribute('port-group')

          // 不允许输入连接到输入，输出连接到输出
          if (sourceGroup?.includes('out') && targetGroup?.includes('out')) {
            return false
          }
          if (sourceGroup?.includes('in') && targetGroup?.includes('in')) {
            return false
          }

          // 执行流只能连接到执行流
          if (sourceGroup?.includes('exec') && !targetGroup?.includes('exec')) {
            return false
          }
          if (!sourceGroup?.includes('exec') && targetGroup?.includes('exec')) {
            return false
          }

          return true
        },
        createEdge() {
          return new Graph.Edge({
            attrs: {
              line: {
                stroke: '#ffffff',
                strokeWidth: 2,
                targetMarker: {
                  name: 'block',
                  width: 8,
                  height: 6,
                },
              },
            },
            zIndex: 0,
          })
        },
        validateConnection({ targetMagnet }) {
          return !!targetMagnet
        },
      },
      highlighting: {
        magnetAdsorbed: {
          name: 'stroke',
          args: {
            attrs: {
              fill: '#5F95FF',
              stroke: '#5F95FF',
            },
          },
        },
      },
      resizing: true,
      rotating: true,
      keyboard: true,
      clipboard: true,
    })

    // 添加小地图插件
    graph.use(
      new MiniMap({
        container: document.getElementById('minimap-container'),
        width: 200,
        height: 160,
        padding: 10,
        graphOptions: {
          async: true,
          getCellView(cell) {
            if (cell.isNode()) {
              return Graph.NodeView
            } else {
              return Graph.EdgeView
            }
          },
          createCellView(cell) {
            if (cell.isEdge()) {
              return new Graph.EdgeView(cell)
            }
          },
        },
      })
    )

    // 注册自定义节点
    registerCustomNodes()

  } catch (error) {
    errorService.handleError(error as Error, 'GraphCanvas.initializeGraph')
  }
}

function registerCustomNodes() {
  if (!graph) return

  // 注册基础节点形状
  Graph.registerNode(
    'blueprint-node',
    {
      inherit: 'rect',
      width: 160,
      height: 80,
      attrs: {
        body: {
          strokeWidth: 2,
          stroke: '#404040',
          fill: '#2a2a2a',
          rx: 8,
          ry: 8,
          filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))',
        },
        text: {
          fontSize: 13,
          fill: '#ffffff',
          fontWeight: 'bold',
          textAnchor: 'middle',
          textVerticalAnchor: 'middle',
          textWrap: {
            width: 140,
            height: 60,
            ellipsis: true,
          },
        },
      },
      ports: {
        groups: {
          // 执行流输入端口（左侧，白色）
          'exec-in': {
            position: {
              name: 'left',
              args: { y: 20 }
            },
            attrs: {
              execInCircle: {
                r: 6,
                magnet: true,
                stroke: '#ffffff',
                strokeWidth: 2,
                fill: '#404040',
                cursor: 'crosshair',
                'port-group': 'exec-in',
              },
              execInText: {
                fontSize: 10,
                fill: '#cccccc',
                textAnchor: 'end',
                x: -12,
                y: 4,
              }
            },
            markup: [
              {
                tagName: 'circle',
                selector: 'execInCircle',
              },
              {
                tagName: 'text',
                selector: 'execInText',
              }
            ],
          },
          // 执行流输出端口（右侧，白色）
          'exec-out': {
            position: {
              name: 'right',
              args: { y: 20 }
            },
            attrs: {
              execOutCircle: {
                r: 6,
                magnet: true,
                stroke: '#ffffff',
                strokeWidth: 2,
                fill: '#404040',
                cursor: 'crosshair',
                'port-group': 'exec-out',
              },
              execOutText: {
                fontSize: 10,
                fill: '#cccccc',
                textAnchor: 'start',
                x: 12,
                y: 4,
              }
            },
            markup: [
              {
                tagName: 'circle',
                selector: 'execOutCircle',
              },
              {
                tagName: 'text',
                selector: 'execOutText',
              }
            ],
          },
          // 数据输入端口（左侧，蓝色）
          'data-in': {
            position: {
              name: 'left',
              args: { y: 50 }
            },
            attrs: {
              dataInCircle: {
                r: 5,
                magnet: true,
                stroke: '#1890ff',
                strokeWidth: 2,
                fill: '#0050b3',
                cursor: 'crosshair',
                'port-group': 'data-in',
              },
              dataInText: {
                fontSize: 10,
                fill: '#cccccc',
                textAnchor: 'end',
                x: -12,
                y: 4,
              }
            },
            markup: [
              {
                tagName: 'circle',
                selector: 'dataInCircle',
              },
              {
                tagName: 'text',
                selector: 'dataInText',
              }
            ],
          },
          // 数据输出端口（右侧，蓝色）
          'data-out': {
            position: {
              name: 'right',
              args: { y: 50 }
            },
            attrs: {
              dataOutCircle: {
                r: 5,
                magnet: true,
                stroke: '#1890ff',
                strokeWidth: 2,
                fill: '#0050b3',
                cursor: 'crosshair',
                'port-group': 'data-out',
              },
              dataOutText: {
                fontSize: 10,
                fill: '#cccccc',
                textAnchor: 'start',
                x: 12,
                y: 4,
              }
            },
            markup: [
              {
                tagName: 'circle',
                selector: 'dataOutCircle',
              },
              {
                tagName: 'text',
                selector: 'dataOutText',
              }
            ],
          },
        },
      },
    },
    true
  )

  // 注册逻辑分支节点（有多个输出端口）
  Graph.registerNode(
    'logic-branch-node',
    {
      inherit: 'blueprint-node',
      height: 100,
      ports: {
        groups: {
          'exec-in': {
            position: {
              name: 'left',
              args: { y: 30 }
            },
            attrs: {
              branchInCircle: {
                r: 6,
                magnet: true,
                stroke: '#ffffff',
                strokeWidth: 2,
                fill: '#404040',
                cursor: 'crosshair',
                'port-group': 'exec-in',
              },
            },
            markup: [{ tagName: 'circle', selector: 'branchInCircle' }],
          },
          'exec-out-true': {
            position: {
              name: 'right',
              args: { y: 20 }
            },
            attrs: {
              trueOutCircle: {
                r: 6,
                magnet: true,
                stroke: '#4CAF50',
                strokeWidth: 2,
                fill: '#1B5E20',
                cursor: 'crosshair',
                'port-group': 'exec-out',
              },
              trueOutText: {
                fontSize: 10,
                fill: '#4CAF50',
                textAnchor: 'start',
                x: 12,
                y: 4,
                text: 'True',
              }
            },
            markup: [
              { tagName: 'circle', selector: 'trueOutCircle' },
              { tagName: 'text', selector: 'trueOutText' }
            ],
          },
          'exec-out-false': {
            position: {
              name: 'right',
              args: { y: 50 }
            },
            attrs: {
              falseOutCircle: {
                r: 6,
                magnet: true,
                stroke: '#F44336',
                strokeWidth: 2,
                fill: '#B71C1C',
                cursor: 'crosshair',
                'port-group': 'exec-out',
              },
              falseOutText: {
                fontSize: 10,
                fill: '#F44336',
                textAnchor: 'start',
                x: 12,
                y: 4,
                text: 'False',
              }
            },
            markup: [
              { tagName: 'circle', selector: 'falseOutCircle' },
              { tagName: 'text', selector: 'falseOutText' }
            ],
          },
        },
      },
    },
    true
  )

  // 设置端口悬停效果
  setupPortHoverEffects()

  // 配置连接线样式
  setupConnectionStyles()
}

function setupPortHoverEffects() {
  if (!graph) return

  // 端口悬停进入效果
  graph.on('node:port:mouseenter', ({ port, node }) => {
    const portGroup = node.getPortProp(port, 'group')

    // 根据端口组确定选择器名称
    let circleSelector = ''
    let textSelector = ''
    let label = ''
    let originalRadius = 6

    switch (portGroup) {
      case 'exec-in':
        circleSelector = 'execInCircle'
        textSelector = 'execInText'
        label = 'Exec In'
        originalRadius = 6
        break
      case 'exec-out':
        circleSelector = 'execOutCircle'
        textSelector = 'execOutText'
        label = 'Exec Out'
        originalRadius = 6
        break
      case 'data-in':
        circleSelector = 'dataInCircle'
        textSelector = 'dataInText'
        label = 'Data In'
        originalRadius = 5
        break
      case 'data-out':
        circleSelector = 'dataOutCircle'
        textSelector = 'dataOutText'
        label = 'Data Out'
        originalRadius = 5
        break
      case 'exec-out-true':
        circleSelector = 'trueOutCircle'
        textSelector = 'trueOutText'
        label = 'True'
        originalRadius = 6
        break
      case 'exec-out-false':
        circleSelector = 'falseOutCircle'
        textSelector = 'falseOutText'
        label = 'False'
        originalRadius = 6
        break
      default:
        // 对于逻辑分支节点的输入端口
        if (node.shape === 'logic-branch-node' && portGroup === 'exec-in') {
          circleSelector = 'branchInCircle'
          label = 'Exec In'
          originalRadius = 6
        }
        break
    }

    if (circleSelector) {
      const portElement = node.getPortProp(port, `attrs/${circleSelector}`)
      if (portElement) {
        // 放大端口并添加发光效果
        node.setPortProp(port, `attrs/${circleSelector}/r`, originalRadius * 1.5)
        node.setPortProp(port, `attrs/${circleSelector}/filter`, 'drop-shadow(0 0 8px currentColor)')

        // 显示端口标签（如果有文本选择器）
        if (textSelector) {
          node.setPortProp(port, `attrs/${textSelector}/text`, label)
        }
      }
    }
  })

  // 端口悬停离开效果
  graph.on('node:port:mouseleave', ({ port, node }) => {
    const portGroup = node.getPortProp(port, 'group')

    // 根据端口组确定选择器名称和原始半径
    let circleSelector = ''
    let textSelector = ''
    let originalRadius = 6

    switch (portGroup) {
      case 'exec-in':
        circleSelector = 'execInCircle'
        textSelector = 'execInText'
        originalRadius = 6
        break
      case 'exec-out':
        circleSelector = 'execOutCircle'
        textSelector = 'execOutText'
        originalRadius = 6
        break
      case 'data-in':
        circleSelector = 'dataInCircle'
        textSelector = 'dataInText'
        originalRadius = 5
        break
      case 'data-out':
        circleSelector = 'dataOutCircle'
        textSelector = 'dataOutText'
        originalRadius = 5
        break
      case 'exec-out-true':
        circleSelector = 'trueOutCircle'
        textSelector = 'trueOutText'
        originalRadius = 6
        break
      case 'exec-out-false':
        circleSelector = 'falseOutCircle'
        textSelector = 'falseOutText'
        originalRadius = 6
        break
      default:
        // 对于逻辑分支节点的输入端口
        if (node.shape === 'logic-branch-node' && portGroup === 'exec-in') {
          circleSelector = 'branchInCircle'
          originalRadius = 6
        }
        break
    }

    if (circleSelector) {
      // 恢复端口原始大小
      node.setPortProp(port, `attrs/${circleSelector}/r`, originalRadius)
      node.setPortProp(port, `attrs/${circleSelector}/filter`, '')

      // 隐藏端口标签（如果有文本选择器）
      if (textSelector) {
        node.setPortProp(port, `attrs/${textSelector}/text`, '')
      }
    }
  })
}

function setupConnectionStyles() {
  if (!graph) return

  // 配置连接线样式
  graph.on('edge:connected', ({ edge }) => {
    const sourcePort = edge.getSourcePortId()
    const targetPort = edge.getTargetPortId()

    // 根据端口类型设置连接线颜色和样式
    if (sourcePort?.includes('exec') || targetPort?.includes('exec')) {
      // 执行流连接线（白色，较粗）
      edge.setAttrs({
        line: {
          stroke: '#ffffff',
          strokeWidth: 3,
          targetMarker: {
            name: 'block',
            width: 8,
            height: 6,
            fill: '#ffffff',
          },
        },
      })
    } else if (sourcePort?.includes('data') || targetPort?.includes('data')) {
      // 数据流连接线（蓝色，较细）
      edge.setAttrs({
        line: {
          stroke: '#1890ff',
          strokeWidth: 2,
          targetMarker: {
            name: 'block',
            width: 6,
            height: 4,
            fill: '#1890ff',
          },
        },
      })
    }

    // 设置贝塞尔曲线连接器
    edge.setConnector('smooth', {
      direction: 'H',
      raw: true,
    })
  })

  // 连接线悬停效果
  graph.on('edge:mouseenter', ({ edge }) => {
    edge.setAttrs({
      line: {
        strokeWidth: edge.getAttrByPath('line/strokeWidth') * 1.5,
        filter: 'drop-shadow(0 0 4px currentColor)',
      },
    })
  })

  graph.on('edge:mouseleave', ({ edge }) => {
    const isExecEdge = edge.getAttrByPath('line/stroke') === '#ffffff'
    edge.setAttrs({
      line: {
        strokeWidth: isExecEdge ? 3 : 2,
        filter: '',
      },
    })
  })
}

function setupEventListeners() {
  if (!graph) return

  // 节点选择事件
  graph.on('node:click', ({ node }) => {
    emit('nodeSelected', node.getData())
  })

  // 画布点击事件（取消选择）
  graph.on('blank:click', () => {
    emit('nodeSelected', null)
  })

  // 图形变化事件
  graph.on('cell:added cell:removed cell:changed', () => {
    emit('graphChanged', graph?.toJSON())
  })

  // 监听添加节点请求
  document.addEventListener('add-node-request', handleAddNodeRequest)
}

function handleAddNodeRequest(event: any) {
  const { type, subtype } = event.detail
  addNode(type, subtype)
}

function addNode(nodeType: string, subtype?: string) {
  if (!graph) return

  try {
    performanceService.startMeasure('GraphCanvas.addNode')

    const nodeData: NodeData = {
      nodeType: nodeType as any,
      displayName: getNodeDisplayName(nodeType, subtype),
      ...(subtype && { subtype }),
    }

    let createdNode: Node | null = null

    const command = new FunctionCommand(
      `添加${nodeData.displayName}节点`,
      () => {
        // 为逻辑分支节点使用特殊形状
        const nodeShape = nodeType === 'logic-branch' ? 'logic-branch-node' : 'blueprint-node'

        createdNode = graph!.addNode({
          shape: nodeShape,
          x: Math.random() * 200 + 100, // 随机位置避免重叠
          y: Math.random() * 200 + 100,
          data: nodeData,
          label: nodeData.displayName,
          ports: getNodePorts(nodeType),
        })

        // 设置节点样式
        setNodeStyle(createdNode, nodeType)

        // 缓存节点数据以提高性能
        cacheService.set(`node-${createdNode.id}`, nodeData, 10 * 60 * 1000)
      },
      () => {
        if (createdNode) {
          graph!.removeCell(createdNode)
          cacheService.delete(`node-${createdNode.id}`)
        }
      }
    )

    undoRedoService.executeCommand(command)
    performanceService.endMeasure('GraphCanvas.addNode')

  } catch (error) {
    errorService.handleError(error as Error, 'GraphCanvas.addNode')
  }
}

function getNodeDisplayName(nodeType: string, subtype?: string): string {
  const typeMap: Record<string, string> = {
    'event': '事件',
    'action-highlight': '高亮',
    'action-callback': '回调',
    'logic-branch': '分支',
    'scene-config': '场景配置',
    'data-source': '数据源',
    'data-transform': '数据转换',
    'data-mapping': 'CSV映射',
    'data-consumer': '数据消费',
    'lifecycle': '生命周期',
  }

  let name = typeMap[nodeType] || nodeType
  if (subtype) {
    name += ` (${subtype})`
  }
  return name
}

function getNodePorts(nodeType: string) {
  const ports: any[] = []

  // 根据节点类型配置端口
  switch (nodeType) {
    case 'scene-config':
      // 场景配置节点只有输出执行端口
      ports.push({ id: 'out-exec', group: 'exec-out' })
      break

    case 'event':
      // 事件节点只有输出执行端口
      ports.push({ id: 'out-exec', group: 'exec-out' })
      break

    case 'action-highlight':
    case 'action-callback':
      // 动作节点有输入和输出执行端口
      ports.push(
        { id: 'in-exec', group: 'exec-in' },
        { id: 'out-exec', group: 'exec-out' }
      )
      break

    case 'logic-branch':
      // 逻辑分支节点有输入执行端口和多个输出端口
      ports.push(
        { id: 'in-exec', group: 'exec-in' },
        { id: 'out-true', group: 'exec-out-true' },
        { id: 'out-false', group: 'exec-out-false' }
      )
      break

    case 'data-source':
      // 数据源节点有输出数据端口
      ports.push(
        { id: 'out-data', group: 'data-out' }
      )
      break

    case 'data-transform':
    case 'data-mapping':
      // 数据转换节点有输入和输出数据端口
      ports.push(
        { id: 'in-data', group: 'data-in' },
        { id: 'out-data', group: 'data-out' }
      )
      break

    case 'data-consumer':
      // 数据消费节点有输入数据端口和执行端口
      ports.push(
        { id: 'in-exec', group: 'exec-in' },
        { id: 'in-data', group: 'data-in' },
        { id: 'out-exec', group: 'exec-out' }
      )
      break

    case 'lifecycle':
      // 生命周期节点只有输出执行端口
      ports.push({ id: 'out-exec', group: 'exec-out' })
      break

    default:
      // 默认配置：输入和输出执行端口
      ports.push(
        { id: 'in-exec', group: 'exec-in' },
        { id: 'out-exec', group: 'exec-out' }
      )
  }

  return ports
}

function setNodeStyle(node: Node, nodeType: string) {
  // 蓝图风格的节点颜色配置
  const styleMap: Record<string, any> = {
    'event': {
      stroke: '#4CAF50',
      fill: '#1B5E20',
      'text/fill': '#ffffff'
    },
    'action-highlight': {
      stroke: '#FF9800',
      fill: '#E65100',
      'text/fill': '#ffffff'
    },
    'action-callback': {
      stroke: '#2196F3',
      fill: '#0D47A1',
      'text/fill': '#ffffff'
    },
    'logic-branch': {
      stroke: '#9C27B0',
      fill: '#4A148C',
      'text/fill': '#ffffff'
    },
    'scene-config': {
      stroke: '#E91E63',
      fill: '#880E4F',
      'text/fill': '#ffffff'
    },
    'data-source': {
      stroke: '#00BCD4',
      fill: '#006064',
      'text/fill': '#ffffff'
    },
    'data-transform': {
      stroke: '#8BC34A',
      fill: '#33691E',
      'text/fill': '#ffffff'
    },
    'data-mapping': {
      stroke: '#FF5722',
      fill: '#BF360C',
      'text/fill': '#ffffff'
    },
    'data-consumer': {
      stroke: '#F44336',
      fill: '#B71C1C',
      'text/fill': '#ffffff'
    },
    'lifecycle': {
      stroke: '#3F51B5',
      fill: '#1A237E',
      'text/fill': '#ffffff'
    },
  }

  const style = styleMap[nodeType] || {
    stroke: '#607D8B',
    fill: '#263238',
    'text/fill': '#ffffff'
  }

  // 应用样式到节点
  node.attr('body', {
    stroke: style.stroke,
    fill: style.fill,
    strokeWidth: 2,
    rx: 8,
    ry: 8,
    filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))',
  })

  node.attr('text', {
    fill: style['text/fill'],
    fontSize: 13,
    fontWeight: 'bold',
  })
}

// 暴露方法给父组件
defineExpose({
  saveGraphData: () => graph?.toJSON(),
  loadGraphData: (data: any) => {
    if (graph && data) {
      graph.fromJSON(data)
    }
  },
  clearGraph: () => {
    if (graph) {
      graph.clearCells()
    }
  },
  getGraph: () => graph,
})
</script>

<style scoped>
.graph-canvas {
  width: 100%;
  height: 100%;
  position: relative;
  background: #1e1e1e;
}

#graph-container {
  width: 100%;
  height: 100%;
}

/* 端口悬停效果 */
:deep(.x6-port-body) {
  transition: all 0.2s ease-in-out;
}

:deep(.x6-port-body:hover) {
  transform: scale(1.2);
  filter: drop-shadow(0 0 8px currentColor);
}

/* 连接线动画效果 */
:deep(.x6-edge-path) {
  transition: all 0.2s ease-in-out;
}

:deep(.x6-edge:hover .x6-edge-path) {
  filter: drop-shadow(0 0 4px currentColor);
}

/* 节点选择效果 */
:deep(.x6-node.selected) {
  filter: drop-shadow(0 0 12px #1890ff);
}

/* 连接预览线样式 */
:deep(.x6-edge-tool-preview) {
  stroke-dasharray: 5, 5;
  animation: dash 1s linear infinite;
}

@keyframes dash {
  to {
    stroke-dashoffset: -10;
  }
}

/* 端口标签样式 */
:deep(.x6-port-label) {
  pointer-events: none;
  user-select: none;
}
</style>
