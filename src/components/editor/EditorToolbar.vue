<template>
  <div class="editor-toolbar">
    <div class="toolbar-group">
      <button
        class="toolbar-btn"
        @click="handleUndo"
        :disabled="!canUndo"
        :title="canUndo ? `撤销: ${undoCommandName} (Ctrl+Z)` : '撤销 (Ctrl+Z)'"
      >
        <span class="icon">↶</span>
        撤销
      </button>
      <button
        class="toolbar-btn"
        @click="handleRedo"
        :disabled="!canRedo"
        :title="canRedo ? `重做: ${redoCommandName} (Ctrl+Y)` : '重做 (Ctrl+Y)'"
      >
        <span class="icon">↷</span>
        重做
      </button>
    </div>

    <div class="toolbar-group">
      <button 
        class="toolbar-btn" 
        @click="handleCopy"
        :disabled="!hasSelection"
        title="复制 (Ctrl+C)"
      >
        <span class="icon">📋</span>
        复制
      </button>
      <button 
        class="toolbar-btn" 
        @click="handlePaste"
        :disabled="!canPaste"
        title="粘贴 (Ctrl+V)"
      >
        <span class="icon">📄</span>
        粘贴
      </button>
      <button 
        class="toolbar-btn danger" 
        @click="handleDelete"
        :disabled="!hasSelection"
        title="删除 (Delete)"
      >
        <span class="icon">🗑️</span>
        删除
      </button>
    </div>

    <div class="toolbar-group">
      <button 
        class="toolbar-btn" 
        @click="handleZoomIn"
        title="放大 (Ctrl++)"
      >
        <span class="icon">🔍+</span>
        放大
      </button>
      <button 
        class="toolbar-btn" 
        @click="handleZoomOut"
        title="缩小 (Ctrl+-)"
      >
        <span class="icon">🔍-</span>
        缩小
      </button>
      <button 
        class="toolbar-btn" 
        @click="handleZoomToFit"
        title="适应画布 (Ctrl+0)"
      >
        <span class="icon">⊞</span>
        适应
      </button>
    </div>

    <div class="toolbar-group">
      <button 
        class="toolbar-btn" 
        @click="handleAlignLeft"
        :disabled="!hasMultipleSelection"
        title="左对齐"
      >
        <span class="icon">⫷</span>
        左对齐
      </button>
      <button 
        class="toolbar-btn" 
        @click="handleAlignCenter"
        :disabled="!hasMultipleSelection"
        title="居中对齐"
      >
        <span class="icon">⫸</span>
        居中
      </button>
      <button 
        class="toolbar-btn" 
        @click="handleAlignRight"
        :disabled="!hasMultipleSelection"
        title="右对齐"
      >
        <span class="icon">⫹</span>
        右对齐
      </button>
    </div>

    <div class="toolbar-group">
      <button 
        class="toolbar-btn primary" 
        @click="handleCompile"
        title="编译配置 (Ctrl+Enter)"
      >
        <span class="icon">⚙️</span>
        编译
      </button>
      <button 
        class="toolbar-btn" 
        @click="handleSave"
        title="保存 (Ctrl+S)"
      >
        <span class="icon">💾</span>
        保存
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import type { Graph } from '@antv/x6'
import { errorService } from '../../services/ErrorService'
import { undoRedoService } from '../../services/UndoRedoService'
import { keyboardService } from '../../services/KeyboardService'

interface Props {
  graph: Graph | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  compile: []
  save: []
}>()

// 状态管理
const canUndo = ref(false)
const canRedo = ref(false)
const hasSelection = ref(false)
const canPaste = ref(false)
const selectedCells = ref<any[]>([])
const undoCommandName = ref<string | null>(null)
const redoCommandName = ref<string | null>(null)

// 计算属性
const hasMultipleSelection = computed(() => selectedCells.value.length > 1)

// 更新撤销/重做状态
function updateUndoRedoState() {
  canUndo.value = undoRedoService.canUndo()
  canRedo.value = undoRedoService.canRedo()
  undoCommandName.value = undoRedoService.getNextUndoCommandName()
  redoCommandName.value = undoRedoService.getNextRedoCommandName()
}

onMounted(() => {
  setupKeyboardShortcuts()
  updateToolbarState()
  updateUndoRedoState()

  // 定期更新撤销/重做状态
  const updateInterval = setInterval(updateUndoRedoState, 100)

  onUnmounted(() => {
    clearInterval(updateInterval)
  })
})

onUnmounted(() => {
  removeKeyboardShortcuts()
})

function setupKeyboardShortcuts() {
  document.addEventListener('keydown', handleKeyDown)
}

function removeKeyboardShortcuts() {
  document.removeEventListener('keydown', handleKeyDown)
}

function handleKeyDown(event: KeyboardEvent) {
  if (!props.graph) return

  const { ctrlKey, metaKey, key } = event
  const isCtrl = ctrlKey || metaKey

  try {
    if (isCtrl) {
      switch (key.toLowerCase()) {
        case 'z':
          event.preventDefault()
          if (event.shiftKey) {
            handleRedo()
          } else {
            handleUndo()
          }
          break
        case 'y':
          event.preventDefault()
          handleRedo()
          break
        case 'c':
          event.preventDefault()
          handleCopy()
          break
        case 'v':
          event.preventDefault()
          handlePaste()
          break
        case 'a':
          event.preventDefault()
          props.graph.selectAll()
          break
        case 's':
          event.preventDefault()
          emit('save')
          break
        case 'enter':
          event.preventDefault()
          emit('compile')
          break
        case '=':
        case '+':
          event.preventDefault()
          handleZoomIn()
          break
        case '-':
          event.preventDefault()
          handleZoomOut()
          break
        case '0':
          event.preventDefault()
          handleZoomToFit()
          break
      }
    } else if (key === 'Delete' || key === 'Backspace') {
      event.preventDefault()
      handleDelete()
    }
  } catch (error) {
    errorService.handleError(error as Error, 'EditorToolbar.handleKeyDown')
  }
}

function updateToolbarState() {
  if (!props.graph) return

  // 更新撤销/重做状态
  canUndo.value = props.graph.canUndo()
  canRedo.value = props.graph.canRedo()

  // 更新选择状态
  selectedCells.value = props.graph.getSelectedCells()
  hasSelection.value = selectedCells.value.length > 0

  // 更新粘贴状态
  canPaste.value = props.graph.isClipboardEmpty() === false
}

// 工具栏操作
function handleUndo() {
  if (canUndo.value) {
    undoRedoService.undo().then(() => {
      updateToolbarState()
      updateUndoRedoState()
    }).catch(error => {
      errorService.handleError(error, 'EditorToolbar.handleUndo')
    })
  }
}

function handleRedo() {
  if (canRedo.value) {
    undoRedoService.redo().then(() => {
      updateToolbarState()
      updateUndoRedoState()
    }).catch(error => {
      errorService.handleError(error, 'EditorToolbar.handleRedo')
    })
  }
}

function handleCopy() {
  if (props.graph && hasSelection.value) {
    props.graph.copy(selectedCells.value)
    updateToolbarState()
  }
}

function handlePaste() {
  if (props.graph && canPaste.value) {
    props.graph.paste()
    updateToolbarState()
  }
}

function handleDelete() {
  if (props.graph && hasSelection.value) {
    props.graph.removeCells(selectedCells.value)
    updateToolbarState()
  }
}

function handleZoomIn() {
  if (props.graph) {
    props.graph.zoom(0.1)
  }
}

function handleZoomOut() {
  if (props.graph) {
    props.graph.zoom(-0.1)
  }
}

function handleZoomToFit() {
  if (props.graph) {
    props.graph.zoomToFit({ padding: 20 })
  }
}

function handleAlignLeft() {
  if (props.graph && hasMultipleSelection.value) {
    const cells = selectedCells.value
    const leftmost = Math.min(...cells.map(cell => cell.getBBox().x))
    cells.forEach(cell => {
      cell.setPosition(leftmost, cell.getBBox().y)
    })
  }
}

function handleAlignCenter() {
  if (props.graph && hasMultipleSelection.value) {
    const cells = selectedCells.value
    const bboxes = cells.map(cell => cell.getBBox())
    const centerX = (Math.min(...bboxes.map(b => b.x)) + Math.max(...bboxes.map(b => b.x + b.width))) / 2
    
    cells.forEach((cell, index) => {
      const bbox = bboxes[index]
      cell.setPosition(centerX - bbox.width / 2, bbox.y)
    })
  }
}

function handleAlignRight() {
  if (props.graph && hasMultipleSelection.value) {
    const cells = selectedCells.value
    const rightmost = Math.max(...cells.map(cell => {
      const bbox = cell.getBBox()
      return bbox.x + bbox.width
    }))
    
    cells.forEach(cell => {
      const bbox = cell.getBBox()
      cell.setPosition(rightmost - bbox.width, bbox.y)
    })
  }
}

function handleCompile() {
  emit('compile')
}

function handleSave() {
  emit('save')
}

// 监听图形变化
if (props.graph) {
  props.graph.on('selection:changed', updateToolbarState)
  props.graph.on('history:change', updateToolbarState)
}
</script>

<style scoped>
.editor-toolbar {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 16px;
  background-color: #2a2a2a;
  border-bottom: 1px solid #444;
  flex-wrap: wrap;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 0 8px;
  border-right: 1px solid #444;
}

.toolbar-group:last-child {
  border-right: none;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background-color: #3a3a3a;
  border: 1px solid #555;
  border-radius: 4px;
  color: #e0e0e0;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.toolbar-btn:hover:not(:disabled) {
  background-color: #4a4a4a;
  border-color: #666;
}

.toolbar-btn:active:not(:disabled) {
  background-color: #2a2a2a;
}

.toolbar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-btn.primary {
  background-color: #1890ff;
  border-color: #1890ff;
  color: white;
}

.toolbar-btn.primary:hover:not(:disabled) {
  background-color: #40a9ff;
}

.toolbar-btn.danger {
  background-color: #ff4d4f;
  border-color: #ff4d4f;
  color: white;
}

.toolbar-btn.danger:hover:not(:disabled) {
  background-color: #ff7875;
}

.icon {
  font-size: 14px;
}
</style>
