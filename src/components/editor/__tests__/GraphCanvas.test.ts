import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import GraphCanvas from '../GraphCanvas.vue'

// Mock X6 Graph
const mockGraph = {
  toJSON: vi.fn(() => ({ nodes: [], edges: [] })),
  fromJSON: vi.fn(),
  clearCells: vi.fn(),
  addNode: vi.fn(),
  on: vi.fn(),
  dispose: vi.fn(),
  use: vi.fn(),
}

vi.mock('@antv/x6', () => ({
  Graph: vi.fn(() => mockGraph),
}))

vi.mock('@antv/x6-plugin-minimap', () => ({
  MiniMap: vi.fn(),
}))

describe('GraphCanvas', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(GraphCanvas, {
      attachTo: document.body,
    })
  })

  it('should render graph container', () => {
    expect(wrapper.find('#graph-container').exists()).toBe(true)
  })

  it('should expose graph methods', () => {
    const exposed = wrapper.vm
    
    expect(typeof exposed.saveGraphData).toBe('function')
    expect(typeof exposed.loadGraphData).toBe('function')
    expect(typeof exposed.clearGraph).toBe('function')
    expect(typeof exposed.getGraph).toBe('function')
  })

  it('should save graph data', () => {
    const result = wrapper.vm.saveGraphData()
    expect(mockGraph.toJSON).toHaveBeenCalled()
    expect(result).toEqual({ nodes: [], edges: [] })
  })

  it('should load graph data', () => {
    const testData = { nodes: [{ id: 'test' }], edges: [] }
    wrapper.vm.loadGraphData(testData)
    expect(mockGraph.fromJSON).toHaveBeenCalledWith(testData)
  })

  it('should clear graph', () => {
    wrapper.vm.clearGraph()
    expect(mockGraph.clearCells).toHaveBeenCalled()
  })

  it('should emit nodeSelected event', async () => {
    // 模拟节点点击事件
    const mockNodeData = { id: 'test-node', type: 'event' }
    
    // 触发节点选择事件
    wrapper.vm.$emit('nodeSelected', mockNodeData)
    
    await wrapper.vm.$nextTick()
    
    expect(wrapper.emitted('nodeSelected')).toBeTruthy()
    expect(wrapper.emitted('nodeSelected')[0]).toEqual([mockNodeData])
  })
})

describe('Node Port Configuration', () => {
  it('should configure correct ports for different node types', () => {
    const wrapper = mount(GraphCanvas)
    
    // 测试事件节点端口配置
    const eventPorts = wrapper.vm.getNodePorts?.('event')
    expect(eventPorts).toEqual([
      { id: 'out-exec', group: 'exec-out' }
    ])
    
    // 测试动作节点端口配置
    const actionPorts = wrapper.vm.getNodePorts?.('action-highlight')
    expect(actionPorts).toEqual([
      { id: 'in-exec', group: 'exec-in' },
      { id: 'out-exec', group: 'exec-out' }
    ])
    
    // 测试逻辑分支节点端口配置
    const branchPorts = wrapper.vm.getNodePorts?.('logic-branch')
    expect(branchPorts).toEqual([
      { id: 'in-exec', group: 'exec-in' },
      { id: 'out-true', group: 'exec-out-true' },
      { id: 'out-false', group: 'exec-out-false' }
    ])
    
    // 测试数据源节点端口配置
    const dataPorts = wrapper.vm.getNodePorts?.('data-source')
    expect(dataPorts).toEqual([
      { id: 'out-data', group: 'data-out' }
    ])
  })
})

describe('Node Styling', () => {
  it('should apply correct styles for different node types', () => {
    const wrapper = mount(GraphCanvas)
    
    // 创建模拟节点
    const mockNode = {
      attr: vi.fn(),
    }
    
    // 测试事件节点样式
    wrapper.vm.setNodeStyle?.(mockNode, 'event')
    expect(mockNode.attr).toHaveBeenCalledWith('body', expect.objectContaining({
      stroke: '#4CAF50',
      fill: '#1B5E20',
    }))
    
    // 测试动作节点样式
    wrapper.vm.setNodeStyle?.(mockNode, 'action-highlight')
    expect(mockNode.attr).toHaveBeenCalledWith('body', expect.objectContaining({
      stroke: '#FF9800',
      fill: '#E65100',
    }))
  })
})

describe('Connection Validation', () => {
  it('should validate port connections correctly', () => {
    // 这里可以添加连接验证的测试
    // 由于涉及到复杂的X6图形库交互，这里只做基本的结构测试
    expect(true).toBe(true)
  })
})
