<script setup lang="ts">
import { ref, computed } from 'vue'
import SearchFilter from './common/SearchFilter.vue'
import type { NodeType } from '../types'

interface NodeItem {
  id: string
  name: string
  title: string
  description: string
  type: NodeType
  category: string
  subtype?: string
  icon: string
}

// 所有可用的节点
const allNodes = ref<NodeItem[]>([
  // 场景配置
  {
    id: 'scene-config',
    name: 'scene-config',
    title: '场景配置',
    description: '配置场景的基本信息、模型和环境',
    type: 'scene-config',
    category: '场景配置',
    icon: 'scene-config-icon'
  },

  // 事件节点
  {
    id: 'event-doubleClick',
    name: 'event',
    title: '双击事件',
    description: '响应鼠标双击操作',
    type: 'event',
    category: '事件节点',
    subtype: 'doubleClick',
    icon: 'event-icon'
  },
  {
    id: 'event-click',
    name: 'event',
    title: '单击事件',
    description: '响应鼠标单击操作',
    type: 'event',
    category: '事件节点',
    subtype: 'click',
    icon: 'event-icon'
  },
  {
    id: 'event-hover',
    name: 'event',
    title: '悬停事件',
    description: '响应鼠标悬停操作',
    type: 'event',
    category: '事件节点',
    subtype: 'hover',
    icon: 'event-icon'
  },
  {
    id: 'event-rightDoubleClick',
    name: 'event',
    title: '右键双击事件',
    description: '响应鼠标右键双击操作',
    type: 'event',
    category: '事件节点',
    subtype: 'rightDoubleClick',
    icon: 'event-icon'
  },

  // 动作节点
  {
    id: 'action-highlight',
    name: 'action-highlight',
    title: '高亮动作',
    description: '高亮显示指定的网格对象',
    type: 'action-highlight',
    category: '动作节点',
    icon: 'action-icon'
  },
  {
    id: 'action-callback-focus',
    name: 'action-callback',
    title: '相机聚焦',
    description: '将相机聚焦到指定对象',
    type: 'action-callback',
    category: '动作节点',
    subtype: 'CameraService.focusToDevice',
    icon: 'action-icon'
  },
  {
    id: 'action-callback-move',
    name: 'action-callback',
    title: '相机移动',
    description: '移动相机到指定位置',
    type: 'action-callback',
    category: '动作节点',
    subtype: 'CameraService.moveCamera',
    icon: 'action-icon'
  },

  // 逻辑节点
  {
    id: 'logic-branch',
    name: 'logic-branch',
    title: '条件分支',
    description: '根据条件执行不同的分支',
    type: 'logic-branch',
    category: '逻辑节点',
    icon: 'logic-icon'
  },

  // 数据节点
  {
    id: 'data-source-polling',
    name: 'data-source',
    title: '轮询数据源',
    description: '定时轮询获取数据',
    type: 'data-source',
    category: '数据节点',
    subtype: 'polling',
    icon: 'data-icon'
  },
  {
    id: 'data-source-websocket',
    name: 'data-source',
    title: 'WebSocket数据源',
    description: '通过WebSocket实时获取数据',
    type: 'data-source',
    category: '数据节点',
    subtype: 'websocket',
    icon: 'data-icon'
  },
  {
    id: 'data-transform-map',
    name: 'data-transform',
    title: '映射转换',
    description: '将数据值映射为其他值',
    type: 'data-transform',
    category: '数据节点',
    subtype: 'map',
    icon: 'data-icon'
  },
  {
    id: 'data-transform-range',
    name: 'data-transform',
    title: '范围转换',
    description: '将数据值转换到指定范围',
    type: 'data-transform',
    category: '数据节点',
    subtype: 'range',
    icon: 'data-icon'
  },
  {
    id: 'data-mapping',
    name: 'data-mapping',
    title: 'CSV映射文件',
    description: '使用CSV文件进行数据映射',
    type: 'data-mapping',
    category: '数据节点',
    icon: 'data-icon'
  },
  {
    id: 'data-consumer-toggle',
    name: 'data-consumer',
    title: '切换可见性',
    description: '根据数据切换对象可见性',
    type: 'data-consumer',
    category: '数据节点',
    subtype: 'toggle',
    icon: 'data-icon'
  },

  // 生命周期节点
  {
    id: 'lifecycle-onActivated',
    name: 'lifecycle',
    title: '场景激活',
    description: '场景激活时触发',
    type: 'lifecycle',
    category: '生命周期',
    subtype: 'onActivated',
    icon: 'lifecycle-icon'
  },
  {
    id: 'lifecycle-onModelLoaded',
    name: 'lifecycle',
    title: '模型加载完成',
    description: '模型加载完成时触发',
    type: 'lifecycle',
    category: '生命周期',
    subtype: 'onModelLoaded',
    icon: 'lifecycle-icon'
  }
])

// 显示模式
const viewMode = ref<'list' | 'grid'>('grid')
const showSearch = ref(false)

// 过滤器选项
const filterOptions = computed(() => {
  const categories = new Set(allNodes.value.map(node => node.category))
  return Array.from(categories).map(category => ({
    value: category,
    label: category
  }))
})

// 触发添加节点事件
function addNode(type: string, subtype?: string) {
  const event = new CustomEvent("add-node-request", {
    detail: { type, subtype },
  });
  document.dispatchEvent(event);
}

// 处理节点选择
function handleNodeSelect(node: NodeItem) {
  addNode(node.name, node.subtype)
}

// 处理搜索动作
function handleSearchAction(data: { action: string; item: NodeItem }) {
  if (data.action === 'select') {
    handleNodeSelect(data.item)
  }
}

// 切换视图模式
function toggleViewMode() {
  viewMode.value = viewMode.value === 'list' ? 'grid' : 'list'
}

// 切换搜索显示
function toggleSearch() {
  showSearch.value = !showSearch.value
}
</script>

<template>
  <div class="node-library">
    <div class="library-header">
      <h3>节点库</h3>
      <div class="header-actions">
        <button
          class="action-btn"
          @click="toggleSearch"
          :class="{ active: showSearch }"
          title="搜索节点"
        >
          🔍
        </button>
        <button
          class="action-btn"
          @click="toggleViewMode"
          :title="viewMode === 'grid' ? '切换到列表视图' : '切换到网格视图'"
        >
          {{ viewMode === 'grid' ? '☰' : '⊞' }}
        </button>
      </div>
    </div>

    <!-- 搜索过滤器 -->
    <div v-if="showSearch" class="search-section">
      <SearchFilter
        :items="allNodes"
        :filters="filterOptions"
        placeholder="搜索节点..."
        :search-fields="['title', 'description', 'category']"
        @select="handleNodeSelect"
        @action="handleSearchAction"
      />
    </div>

    <!-- 传统分类视图 -->
    <div v-if="!showSearch" class="traditional-view">
      <div class="library-section">
        <h4>场景配置</h4>
        <div class="node-item" @click="addNode('scene-config')">
          <div class="node-icon scene-config-icon"></div>
          <div class="node-label">场景配置</div>
        </div>
      </div>

      <div class="library-section">
        <h4>事件节点</h4>
        <div class="node-item" @click="addNode('event', 'doubleClick')">
          <div class="node-icon event-icon"></div>
          <div class="node-label">双击事件</div>
        </div>
        <div class="node-item" @click="addNode('event', 'click')">
          <div class="node-icon event-icon"></div>
          <div class="node-label">单击事件</div>
        </div>
        <div class="node-item" @click="addNode('event', 'hover')">
          <div class="node-icon event-icon"></div>
          <div class="node-label">悬停事件</div>
        </div>
        <div class="node-item" @click="addNode('event', 'rightDoubleClick')">
          <div class="node-icon event-icon"></div>
          <div class="node-label">右键双击事件</div>
        </div>
      </div>

      <div class="library-section">
        <h4>动作节点</h4>
        <div class="node-item" @click="addNode('action-highlight')">
          <div class="node-icon action-icon"></div>
          <div class="node-label">高亮</div>
        </div>
        <div
          class="node-item"
          @click="addNode('action-callback', 'CameraService.focusToDevice')"
        >
          <div class="node-icon action-icon"></div>
          <div class="node-label">相机聚焦</div>
        </div>
        <div
          class="node-item"
          @click="addNode('action-callback', 'CameraService.moveCamera')"
        >
          <div class="node-icon action-icon"></div>
          <div class="node-label">相机移动</div>
        </div>
      </div>

      <div class="library-section">
        <h4>逻辑控制</h4>
        <div class="node-item" @click="addNode('logic-branch')">
          <div class="node-icon logic-icon"></div>
          <div class="node-label">条件分支</div>
        </div>
      </div>

      <div class="library-section">
        <h4>数据节点</h4>
        <div class="node-item" @click="addNode('data-source', 'polling')">
          <div class="node-icon data-icon"></div>
          <div class="node-label">轮询数据源</div>
        </div>
        <div class="node-item" @click="addNode('data-source', 'websocket')">
          <div class="node-icon data-icon"></div>
          <div class="node-label">WebSocket数据源</div>
        </div>
        <div class="node-item" @click="addNode('data-transform', 'map')">
          <div class="node-icon transform-icon"></div>
          <div class="node-label">映射转换</div>
        </div>
        <div class="node-item" @click="addNode('data-mapping')">
          <div class="node-icon mapping-icon"></div>
          <div class="node-label">CSV映射文件</div>
        </div>
      </div>

      <div class="library-section">
        <h4>生命周期</h4>
        <div class="node-item" @click="addNode('lifecycle', 'onActivated')">
          <div class="node-icon lifecycle-icon"></div>
          <div class="node-label">场景激活</div>
        </div>
        <div class="node-item" @click="addNode('lifecycle', 'onModelLoaded')">
          <div class="node-icon lifecycle-icon"></div>
          <div class="node-label">模型加载完成</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.node-library {
  height: 100%;
  padding: 1rem;
  background-color: #1e1e1e;
  color: #e0e0e0;
  overflow-y: auto;
}

.library-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #444;
}

.library-header h3 {
  margin: 0;
  color: #ffffff;
  font-size: 18px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 6px 8px;
  background: #3a3a3a;
  border: 1px solid #555;
  border-radius: 4px;
  color: #e0e0e0;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.action-btn:hover {
  background: #4a4a4a;
  border-color: #666;
}

.action-btn.active {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}

.search-section {
  margin-bottom: 16px;
}

h3 {
  margin-top: 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #444;
}

.library-section {
  margin-bottom: 1.5rem;
}

.library-section h4 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: #aaa;
  font-size: 0.9rem;
}

.node-item {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  background-color: #2a2a2a;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.node-item:hover {
  background-color: #3a3a3a;
}

.node-icon {
  width: 24px;
  height: 24px;
  margin-right: 0.5rem;
  border-radius: 4px;
}

.event-icon {
  background-color: #4a6b8a;
}

.action-icon {
  background-color: #4a8a6b;
}

.logic-icon {
  background-color: #8a4a6b;
}

.scene-config-icon {
  background-color: #1a3f5c;
}

.data-icon {
  background-color: #0066cc;
}

.transform-icon {
  background-color: #13c2c2;
}

.mapping-icon {
  background-color: #722ed1;
}

.consumer-icon {
  background-color: #fa8c16;
}

.lifecycle-icon {
  background-color: #722ed1;
}

.node-label {
  font-size: 0.9rem;
}
</style>
