import type { Cell } from '@antv/x6'

// 节点类型定义
export type NodeType =
  | 'event'
  | 'action-highlight'
  | 'action-callback'
  | 'logic-branch'
  | 'logic-sequence'
  | 'scene-config'
  | 'data-source'
  | 'data-transform'
  | 'data-mapping'
  | 'data-consumer'
  | 'lifecycle'

// 基础节点数据接口
export interface NodeData {
  nodeType: NodeType
  displayName?: string
  description?: string
  [key: string]: any
}

// 事件节点数据
export interface EventNodeData extends NodeData {
  nodeType: 'event'
  actionType: 'hover' | 'click' | 'doubleClick' | 'rightClick' | 'rightDoubleClick' | 'hotkey'
  meshNames?: string[]
  key?: string // for hotkey events
}

// 动作节点数据
export interface ActionHighlightNodeData extends NodeData {
  nodeType: 'action-highlight'
  color: [number, number, number]
  duration?: number
  intensity?: number
  enabled?: boolean
}

export interface ActionCallbackNodeData extends NodeData {
  nodeType: 'action-callback'
  callback: string
  parameters?: Record<string, any>
}

// 场景配置节点数据
export interface SceneConfigNodeData extends NodeData {
  nodeType: 'scene-config'
  sceneName: string
  models: string[]
  scene?: string
  environment?: string
  camera?: any
  lifecycle?: Record<string, any[]>
}

// 数据节点数据
export interface DataSourceNodeData extends NodeData {
  nodeType: 'data-source'
  dataSource: {
    type: 'polling' | 'websocket'
    interval?: number
    url?: string
    method?: string
    params?: Record<string, any>
    headers?: Record<string, string>
    transforms?: any[]
  }
}

export interface DataTransformNodeData extends NodeData {
  nodeType: 'data-transform'
  transformType: 'map' | 'range'
  transform: {
    input: string
    output: string
    mapping?: Record<string, any>
    from?: [number, number]
    to?: [number, number]
  }
}

export interface DataMappingNodeData extends NodeData {
  nodeType: 'data-mapping'
  mappingFile: string
  match?: Record<string, any>
}

export interface DataConsumerNodeData extends NodeData {
  nodeType: 'data-consumer'
  callback: string
  parameters?: Record<string, any>
  triggerCondition?: 'onChange' | 'always' | 'onTrue' | 'onFalse'
}

// 生命周期节点数据
export interface LifecycleNodeData extends NodeData {
  nodeType: 'lifecycle'
  lifecycleType: 'onActivated' | 'onDeactivated' | 'onModelLoaded' | 'onInit'
  trigger?: 'immediate' | 'delayed'
  delay?: number
}

// 场景接口
export interface Scene {
  id: string
  name: string
  graphData: any
  lastModified: number
  configId?: string
  models?: string[]
  environment?: string
  scene?: string
}

// 编译配置接口
export interface CompiledConfig {
  scenes?: { [key: string]: SceneConfig }
  templates?: { [key: string]: any }
  defaultScene?: string
  authServer?: string
  authPage?: string
}

export interface SceneConfig {
  id?: string
  name?: string
  models?: string[]
  actions?: ActionConfig[]
  scene?: string
  envTemplate?: any
  camera?: any
  staticLabels?: any[]
  lifecycle?: {
    onActivated?: any[]
    onDeactivated?: any[]
    onModelLoaded?: any[]
    onInit?: any[]
  }
  [key: string]: any
}

export interface ActionConfig {
  meshNames?: string[] | { $ref: string }
  actionType?: string
  config?: {
    highlight?: {
      color?: number[]
      duration?: number
      enabled?: boolean
      intensity?: number
    }
    callback?: string
    parameters?: any
    callbacks?: any[]
    description?: string
  }
  [key: string]: any
}

// 组件事件接口
export interface NodeSelectedEvent {
  node: Cell.Properties | null
}

export interface ConfigGeneratedEvent {
  config: CompiledConfig
}

export interface NodeTypeChangedEvent {
  nodeId: string
  newType: NodeType
  oldType: NodeType
}

export interface SceneEvent {
  sceneId: string
  scene?: Scene
}

// 错误类型
export interface AppError {
  code: string
  message: string
  details?: any
  timestamp: number
}

// 应用状态接口
export interface AppState {
  selectedNode: Cell.Properties | null
  currentSceneId: string | null
  generatedConfig: string
  isLibraryCollapsed: boolean
  isPropertiesCollapsed: boolean
}

// 工具函数类型
export type EventHandler<T = any> = (event: T) => void
export type AsyncEventHandler<T = any> = (event: T) => Promise<void>

// 常量
export const NODE_TYPES = [
  { value: 'event', label: '事件节点' },
  { value: 'action-highlight', label: '高亮动作' },
  { value: 'action-callback', label: '回调动作' },
  { value: 'logic-branch', label: '条件分支' },
  { value: 'scene-config', label: '场景配置' },
  { value: 'data-source', label: '数据源' },
  { value: 'data-transform', label: '数据转换' },
  { value: 'data-mapping', label: 'CSV映射文件' },
  { value: 'data-consumer', label: '数据消费' },
  { value: 'lifecycle', label: '生命周期' },
] as const
