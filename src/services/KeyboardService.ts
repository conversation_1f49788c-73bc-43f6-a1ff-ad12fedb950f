export interface KeyboardShortcut {
  id: string
  keys: string[]
  description: string
  handler: (event: KeyboardEvent) => void | Promise<void>
  enabled: boolean
  context?: string // 上下文，用于在特定场景下启用快捷键
}

export interface KeyboardServiceOptions {
  preventDefault?: boolean
  stopPropagation?: boolean
}

export class KeyboardService {
  private static instance: KeyboardService
  private shortcuts: Map<string, KeyboardShortcut> = new Map()
  private pressedKeys: Set<string> = new Set()
  private currentContext: string | null = null
  private isListening = false

  private constructor() {
    this.setupEventListeners()
  }

  static getInstance(): KeyboardService {
    if (!KeyboardService.instance) {
      KeyboardService.instance = new KeyboardService()
    }
    return KeyboardService.instance
  }

  /**
   * 注册快捷键
   */
  register(shortcut: Omit<KeyboardShortcut, 'id'>): string {
    const id = `shortcut-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const fullShortcut: KeyboardShortcut = {
      id,
      ...shortcut,
    }
    
    this.shortcuts.set(id, fullShortcut)
    return id
  }

  /**
   * 注销快捷键
   */
  unregister(id: string): boolean {
    return this.shortcuts.delete(id)
  }

  /**
   * 启用/禁用快捷键
   */
  setEnabled(id: string, enabled: boolean): boolean {
    const shortcut = this.shortcuts.get(id)
    if (shortcut) {
      shortcut.enabled = enabled
      return true
    }
    return false
  }

  /**
   * 设置当前上下文
   */
  setContext(context: string | null): void {
    this.currentContext = context
  }

  /**
   * 获取当前上下文
   */
  getContext(): string | null {
    return this.currentContext
  }

  /**
   * 获取所有快捷键
   */
  getAllShortcuts(): KeyboardShortcut[] {
    return Array.from(this.shortcuts.values())
  }

  /**
   * 获取当前上下文的快捷键
   */
  getContextShortcuts(context?: string): KeyboardShortcut[] {
    const targetContext = context || this.currentContext
    return this.getAllShortcuts().filter(shortcut => 
      shortcut.enabled && (
        !shortcut.context || 
        shortcut.context === targetContext
      )
    )
  }

  /**
   * 开始监听键盘事件
   */
  startListening(): void {
    if (this.isListening) return
    
    document.addEventListener('keydown', this.handleKeyDown)
    document.addEventListener('keyup', this.handleKeyUp)
    document.addEventListener('blur', this.handleBlur)
    window.addEventListener('blur', this.handleBlur)
    
    this.isListening = true
  }

  /**
   * 停止监听键盘事件
   */
  stopListening(): void {
    if (!this.isListening) return
    
    document.removeEventListener('keydown', this.handleKeyDown)
    document.removeEventListener('keyup', this.handleKeyUp)
    document.removeEventListener('blur', this.handleBlur)
    window.removeEventListener('blur', this.handleBlur)
    
    this.isListening = false
    this.pressedKeys.clear()
  }

  /**
   * 检查快捷键是否匹配当前按键
   */
  private isShortcutMatch(shortcut: KeyboardShortcut): boolean {
    if (shortcut.keys.length !== this.pressedKeys.size) {
      return false
    }

    return shortcut.keys.every(key => this.pressedKeys.has(this.normalizeKey(key)))
  }

  /**
   * 标准化按键名称
   */
  private normalizeKey(key: string): string {
    const keyMap: Record<string, string> = {
      'ctrl': 'Control',
      'cmd': 'Meta',
      'alt': 'Alt',
      'shift': 'Shift',
      'space': ' ',
      'enter': 'Enter',
      'esc': 'Escape',
      'tab': 'Tab',
      'backspace': 'Backspace',
      'delete': 'Delete',
      'up': 'ArrowUp',
      'down': 'ArrowDown',
      'left': 'ArrowLeft',
      'right': 'ArrowRight',
    }

    const normalized = key.toLowerCase()
    return keyMap[normalized] || key.toUpperCase()
  }

  /**
   * 处理按键按下事件
   */
  private handleKeyDown = (event: KeyboardEvent): void => {
    // 添加按键到已按下的集合
    this.pressedKeys.add(event.key)
    
    // 添加修饰键
    if (event.ctrlKey) this.pressedKeys.add('Control')
    if (event.metaKey) this.pressedKeys.add('Meta')
    if (event.altKey) this.pressedKeys.add('Alt')
    if (event.shiftKey) this.pressedKeys.add('Shift')

    // 检查是否有匹配的快捷键
    const matchedShortcuts = this.getContextShortcuts().filter(shortcut => 
      this.isShortcutMatch(shortcut)
    )

    if (matchedShortcuts.length > 0) {
      event.preventDefault()
      event.stopPropagation()

      // 执行第一个匹配的快捷键（按注册顺序）
      const shortcut = matchedShortcuts[0]
      try {
        shortcut.handler(event)
      } catch (error) {
        console.error(`Error executing shortcut "${shortcut.description}":`, error)
      }
    }
  }

  /**
   * 处理按键释放事件
   */
  private handleKeyUp = (event: KeyboardEvent): void => {
    this.pressedKeys.delete(event.key)
    
    // 移除修饰键
    if (!event.ctrlKey) this.pressedKeys.delete('Control')
    if (!event.metaKey) this.pressedKeys.delete('Meta')
    if (!event.altKey) this.pressedKeys.delete('Alt')
    if (!event.shiftKey) this.pressedKeys.delete('Shift')
  }

  /**
   * 处理失焦事件
   */
  private handleBlur = (): void => {
    this.pressedKeys.clear()
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    this.startListening()
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.stopListening()
    this.shortcuts.clear()
  }
}

// 创建全局实例
export const keyboardService = KeyboardService.getInstance()

// 预定义的常用快捷键组合
export const CommonShortcuts = {
  SAVE: ['ctrl', 's'],
  COPY: ['ctrl', 'c'],
  PASTE: ['ctrl', 'v'],
  CUT: ['ctrl', 'x'],
  UNDO: ['ctrl', 'z'],
  REDO: ['ctrl', 'y'],
  SELECT_ALL: ['ctrl', 'a'],
  DELETE: ['delete'],
  ESCAPE: ['esc'],
  ENTER: ['enter'],
  ZOOM_IN: ['ctrl', '+'],
  ZOOM_OUT: ['ctrl', '-'],
  ZOOM_FIT: ['ctrl', '0'],
} as const

// 快捷键注册辅助函数
export function registerShortcut(
  keys: string[],
  description: string,
  handler: (event: KeyboardEvent) => void | Promise<void>,
  context?: string
): string {
  return keyboardService.register({
    keys,
    description,
    handler,
    enabled: true,
    context,
  })
}

// 批量注册快捷键
export function registerShortcuts(shortcuts: Array<{
  keys: string[]
  description: string
  handler: (event: KeyboardEvent) => void | Promise<void>
  context?: string
}>): string[] {
  return shortcuts.map(shortcut => registerShortcut(
    shortcut.keys,
    shortcut.description,
    shortcut.handler,
    shortcut.context
  ))
}
