export interface PerformanceMetric {
  name: string
  startTime: number
  endTime?: number
  duration?: number
  metadata?: Record<string, any>
}

export class PerformanceService {
  private static instance: PerformanceService
  private metrics: Map<string, PerformanceMetric> = new Map()
  private observers: PerformanceObserver[] = []

  private constructor() {
    this.initializeObservers()
  }

  static getInstance(): PerformanceService {
    if (!PerformanceService.instance) {
      PerformanceService.instance = new PerformanceService()
    }
    return PerformanceService.instance
  }

  /**
   * 开始性能测量
   */
  startMeasure(name: string, metadata?: Record<string, any>): void {
    const metric: PerformanceMetric = {
      name,
      startTime: performance.now(),
      metadata,
    }
    this.metrics.set(name, metric)
    
    // 使用 Performance API 标记
    if (typeof performance.mark === 'function') {
      performance.mark(`${name}-start`)
    }
  }

  /**
   * 结束性能测量
   */
  endMeasure(name: string): PerformanceMetric | null {
    const metric = this.metrics.get(name)
    if (!metric) {
      console.warn(`Performance metric "${name}" not found`)
      return null
    }

    metric.endTime = performance.now()
    metric.duration = metric.endTime - metric.startTime

    // 使用 Performance API 测量
    if (typeof performance.mark === 'function' && typeof performance.measure === 'function') {
      performance.mark(`${name}-end`)
      performance.measure(name, `${name}-start`, `${name}-end`)
    }

    // 在开发环境下记录性能指标
    if (import.meta.env.DEV) {
      console.log(`⏱️ ${name}: ${metric.duration?.toFixed(2)}ms`, metric.metadata)
    }

    return metric
  }

  /**
   * 异步操作性能测量装饰器
   */
  async measureAsync<T>(
    name: string,
    operation: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    this.startMeasure(name, metadata)
    try {
      const result = await operation()
      this.endMeasure(name)
      return result
    } catch (error) {
      this.endMeasure(name)
      throw error
    }
  }

  /**
   * 同步操作性能测量装饰器
   */
  measureSync<T>(
    name: string,
    operation: () => T,
    metadata?: Record<string, any>
  ): T {
    this.startMeasure(name, metadata)
    try {
      const result = operation()
      this.endMeasure(name)
      return result
    } catch (error) {
      this.endMeasure(name)
      throw error
    }
  }

  /**
   * 获取所有性能指标
   */
  getAllMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values()).filter(metric => metric.duration !== undefined)
  }

  /**
   * 获取特定指标
   */
  getMetric(name: string): PerformanceMetric | undefined {
    return this.metrics.get(name)
  }

  /**
   * 清除所有指标
   */
  clearMetrics(): void {
    this.metrics.clear()
    if (typeof performance.clearMarks === 'function') {
      performance.clearMarks()
    }
    if (typeof performance.clearMeasures === 'function') {
      performance.clearMeasures()
    }
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport(): {
    metrics: PerformanceMetric[]
    summary: {
      totalOperations: number
      averageDuration: number
      slowestOperation: PerformanceMetric | null
      fastestOperation: PerformanceMetric | null
    }
  } {
    const metrics = this.getAllMetrics()
    const durations = metrics.map(m => m.duration!).filter(d => d !== undefined)
    
    const summary = {
      totalOperations: metrics.length,
      averageDuration: durations.length > 0 ? durations.reduce((a, b) => a + b, 0) / durations.length : 0,
      slowestOperation: metrics.reduce((prev, current) => 
        (prev.duration || 0) > (current.duration || 0) ? prev : current, metrics[0]) || null,
      fastestOperation: metrics.reduce((prev, current) => 
        (prev.duration || 0) < (current.duration || 0) ? prev : current, metrics[0]) || null,
    }

    return { metrics, summary }
  }

  /**
   * 初始化性能观察器
   */
  private initializeObservers(): void {
    if (typeof PerformanceObserver === 'undefined') {
      return
    }

    // 观察长任务
    try {
      const longTaskObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 50) { // 超过50ms的任务
            console.warn(`🐌 Long task detected: ${entry.name} (${entry.duration.toFixed(2)}ms)`)
          }
        }
      })
      longTaskObserver.observe({ entryTypes: ['longtask'] })
      this.observers.push(longTaskObserver)
    } catch (error) {
      // Long task API 可能不被支持
    }

    // 观察导航性能
    try {
      const navigationObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          console.log('📊 Navigation timing:', {
            domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
            loadComplete: entry.loadEventEnd - entry.loadEventStart,
            totalTime: entry.loadEventEnd - entry.fetchStart,
          })
        }
      })
      navigationObserver.observe({ entryTypes: ['navigation'] })
      this.observers.push(navigationObserver)
    } catch (error) {
      // Navigation API 可能不被支持
    }
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    this.clearMetrics()
  }
}

// 创建全局实例
export const performanceService = PerformanceService.getInstance()

// 性能测量装饰器
export function measurePerformance(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const measureName = name || `${target.constructor.name}.${propertyKey}`

    descriptor.value = function (...args: any[]) {
      return performanceService.measureSync(measureName, () => originalMethod.apply(this, args))
    }

    return descriptor
  }
}

// 异步性能测量装饰器
export function measureAsyncPerformance(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const measureName = name || `${target.constructor.name}.${propertyKey}`

    descriptor.value = async function (...args: any[]) {
      return performanceService.measureAsync(measureName, () => originalMethod.apply(this, args))
    }

    return descriptor
  }
}
