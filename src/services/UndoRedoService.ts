export interface Command {
  id: string
  name: string
  timestamp: number
  execute(): void | Promise<void>
  undo(): void | Promise<void>
  canMerge?(other: Command): boolean
  merge?(other: Command): Command
}

export interface UndoRedoOptions {
  maxHistorySize?: number
  mergeTimeWindow?: number // Time window for merging commands in milliseconds
}

export class UndoRedoService {
  private static instance: UndoRedoService
  private undoStack: Command[] = []
  private redoStack: Command[] = []
  private options: Required<UndoRedoOptions>
  private isExecuting = false

  private constructor(options: UndoRedoOptions = {}) {
    this.options = {
      maxHistorySize: options.maxHistorySize || 50,
      mergeTimeWindow: options.mergeTimeWindow || 1000, // 1 second
    }
  }

  static getInstance(options?: UndoRedoOptions): UndoRedoService {
    if (!UndoRedoService.instance) {
      UndoRedoService.instance = new UndoRedoService(options)
    }
    return UndoRedoService.instance
  }

  /**
   * 执行命令并添加到历史记录
   */
  async executeCommand(command: Command): Promise<void> {
    if (this.isExecuting) {
      return
    }

    this.isExecuting = true
    try {
      await command.execute()
      this.addToHistory(command)
    } finally {
      this.isExecuting = false
    }
  }

  /**
   * 撤销上一个命令
   */
  async undo(): Promise<boolean> {
    if (this.undoStack.length === 0 || this.isExecuting) {
      return false
    }

    const command = this.undoStack.pop()!
    this.isExecuting = true

    try {
      await command.undo()
      this.redoStack.push(command)
      this.trimRedoStack()
      return true
    } catch (error) {
      // 如果撤销失败，将命令放回撤销栈
      this.undoStack.push(command)
      throw error
    } finally {
      this.isExecuting = false
    }
  }

  /**
   * 重做下一个命令
   */
  async redo(): Promise<boolean> {
    if (this.redoStack.length === 0 || this.isExecuting) {
      return false
    }

    const command = this.redoStack.pop()!
    this.isExecuting = true

    try {
      await command.execute()
      this.undoStack.push(command)
      this.trimUndoStack()
      return true
    } catch (error) {
      // 如果重做失败，将命令放回重做栈
      this.redoStack.push(command)
      throw error
    } finally {
      this.isExecuting = false
    }
  }

  /**
   * 检查是否可以撤销
   */
  canUndo(): boolean {
    return this.undoStack.length > 0 && !this.isExecuting
  }

  /**
   * 检查是否可以重做
   */
  canRedo(): boolean {
    return this.redoStack.length > 0 && !this.isExecuting
  }

  /**
   * 获取撤销栈的大小
   */
  getUndoStackSize(): number {
    return this.undoStack.length
  }

  /**
   * 获取重做栈的大小
   */
  getRedoStackSize(): number {
    return this.redoStack.length
  }

  /**
   * 获取下一个可撤销的命令名称
   */
  getNextUndoCommandName(): string | null {
    const command = this.undoStack[this.undoStack.length - 1]
    return command ? command.name : null
  }

  /**
   * 获取下一个可重做的命令名称
   */
  getNextRedoCommandName(): string | null {
    const command = this.redoStack[this.redoStack.length - 1]
    return command ? command.name : null
  }

  /**
   * 清空历史记录
   */
  clear(): void {
    this.undoStack = []
    this.redoStack = []
  }

  /**
   * 获取历史记录统计
   */
  getHistory(): {
    undoCommands: Array<{ name: string; timestamp: number }>
    redoCommands: Array<{ name: string; timestamp: number }>
    totalCommands: number
  } {
    return {
      undoCommands: this.undoStack.map(cmd => ({ name: cmd.name, timestamp: cmd.timestamp })),
      redoCommands: this.redoStack.map(cmd => ({ name: cmd.name, timestamp: cmd.timestamp })),
      totalCommands: this.undoStack.length + this.redoStack.length,
    }
  }

  /**
   * 添加命令到历史记录
   */
  private addToHistory(command: Command): void {
    // 尝试与最后一个命令合并
    const lastCommand = this.undoStack[this.undoStack.length - 1]
    if (lastCommand && this.canMergeCommands(lastCommand, command)) {
      const mergedCommand = lastCommand.merge!(command)
      this.undoStack[this.undoStack.length - 1] = mergedCommand
    } else {
      this.undoStack.push(command)
      this.trimUndoStack()
    }

    // 清空重做栈（新命令执行后不能重做之前的操作）
    this.redoStack = []
  }

  /**
   * 检查两个命令是否可以合并
   */
  private canMergeCommands(cmd1: Command, cmd2: Command): boolean {
    if (!cmd1.canMerge || !cmd1.merge) {
      return false
    }

    // 检查时间窗口
    const timeDiff = cmd2.timestamp - cmd1.timestamp
    if (timeDiff > this.options.mergeTimeWindow) {
      return false
    }

    return cmd1.canMerge(cmd2)
  }

  /**
   * 修剪撤销栈以保持在最大大小内
   */
  private trimUndoStack(): void {
    while (this.undoStack.length > this.options.maxHistorySize) {
      this.undoStack.shift()
    }
  }

  /**
   * 修剪重做栈以保持在最大大小内
   */
  private trimRedoStack(): void {
    while (this.redoStack.length > this.options.maxHistorySize) {
      this.redoStack.shift()
    }
  }
}

// 创建全局实例
export const undoRedoService = UndoRedoService.getInstance({
  maxHistorySize: 100,
  mergeTimeWindow: 500, // 500ms
})

// 基础命令类
export abstract class BaseCommand implements Command {
  public readonly id: string
  public readonly timestamp: number

  constructor(
    public readonly name: string,
    id?: string
  ) {
    this.id = id || `cmd-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    this.timestamp = Date.now()
  }

  abstract execute(): void | Promise<void>
  abstract undo(): void | Promise<void>

  canMerge?(other: Command): boolean {
    return false
  }

  merge?(other: Command): Command {
    return this
  }
}

// 复合命令类（用于批量操作）
export class CompositeCommand extends BaseCommand {
  constructor(
    name: string,
    private commands: Command[]
  ) {
    super(name)
  }

  async execute(): Promise<void> {
    for (const command of this.commands) {
      await command.execute()
    }
  }

  async undo(): Promise<void> {
    // 反向执行撤销
    for (let i = this.commands.length - 1; i >= 0; i--) {
      await this.commands[i].undo()
    }
  }
}

// 函数式命令类
export class FunctionCommand extends BaseCommand {
  constructor(
    name: string,
    private executeFunc: () => void | Promise<void>,
    private undoFunc: () => void | Promise<void>
  ) {
    super(name)
  }

  async execute(): Promise<void> {
    await this.executeFunc()
  }

  async undo(): Promise<void> {
    await this.undoFunc()
  }
}
