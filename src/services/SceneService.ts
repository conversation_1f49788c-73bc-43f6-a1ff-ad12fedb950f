import { reactive, ref } from "vue";

// 声明全局window.$config类型
declare global {
  interface Window {
    $config?: any;
  }
}

// 场景数据接口
export interface Scene {
  id: string;
  name: string;
  graphData: any;
  lastModified: number;
  models?: string[];
  environment?: string;
  scene?: string;
  configId?: string; // 对应config.js中的场景ID
}

class SceneService {
  private scenes = reactive<Map<string, Scene>>(new Map());
  private currentSceneId = ref<string | null>(null);

  // 获取所有场景
  getAllScenes(): Scene[] {
    return Array.from(this.scenes.values());
  }

  // 获取当前场景
  getCurrentScene(): Scene | null {
    if (!this.currentSceneId.value) return null;
    return this.scenes.get(this.currentSceneId.value) || null;
  }

  // 获取当前场景ID
  getCurrentSceneId(): string | null {
    return this.currentSceneId.value;
  }

  // 设置当前场景
  setCurrentScene(sceneId: string): void {
    if (this.scenes.has(sceneId)) {
      this.currentSceneId.value = sceneId;
    }
  }

  // 创建新场景
  createScene(
    name: string,
    configId?: string,
    models?: string[],
    environment?: string,
    scene?: string
  ): Scene {
    const id = `scene-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    const newScene: Scene = {
      id,
      name,
      graphData: null,
      lastModified: Date.now(),
      configId,
      models,
      environment,
      scene,
    };

    this.scenes.set(id, newScene);
    this.currentSceneId.value = id;
    return newScene;
  }

  // 更新场景
  updateScene(sceneId: string, data: Partial<Scene>): void {
    const scene = this.scenes.get(sceneId);
    if (scene) {
      Object.assign(scene, data, { lastModified: Date.now() });
    }
  }

  // 更新场景图数据
  updateGraphData(sceneId: string, graphData: any): void {
    const scene = this.scenes.get(sceneId);
    if (scene) {
      scene.graphData = graphData;
      scene.lastModified = Date.now();
    }
  }

  // 删除场景
  deleteScene(sceneId: string): boolean {
    if (!this.scenes.has(sceneId)) {
      return false;
    }

    const result = this.scenes.delete(sceneId);

    // 如果删除的是当前场景，切换到第一个场景
    if (result && this.currentSceneId.value === sceneId) {
      const firstScene = this.getAllScenes()[0];
      this.currentSceneId.value = firstScene ? firstScene.id : null;
    }

    return result;
  }

  // 保存所有场景到本地存储
  saveToLocalStorage(): void {
    const scenesArray = this.getAllScenes();
    localStorage.setItem("ddd-flow-scenes", JSON.stringify(scenesArray));
    localStorage.setItem(
      "ddd-flow-current-scene",
      this.currentSceneId.value || ""
    );
  }

  // 从本地存储加载场景
  loadFromLocalStorage(): void {
    try {
      const scenesJson = localStorage.getItem("ddd-flow-scenes");
      const currentSceneId = localStorage.getItem("ddd-flow-current-scene");

      if (scenesJson) {
        const scenesArray = JSON.parse(scenesJson) as Scene[];
        this.scenes.clear();

        scenesArray.forEach((scene) => {
          this.scenes.set(scene.id, scene);
        });

        if (currentSceneId && this.scenes.has(currentSceneId)) {
          this.currentSceneId.value = currentSceneId;
        } else if (scenesArray.length > 0) {
          this.currentSceneId.value = scenesArray[0].id;
        }
      }
    } catch (error) {
      console.error("Failed to load scenes from localStorage:", error);
    }
  }

  // 从config.js导入场景配置
  async importFromConfig(): Promise<boolean> {
    try {
      // 尝试从window对象获取配置
      if (typeof window !== "undefined" && window.$config) {
        this.importFromConfigObject(window.$config);
        return true;
      }

      // 如果window.$config不存在，尝试从文件加载
      try {
        const response = await fetch("/config/config.js");
        if (!response.ok) {
          console.warn("无法加载config.js文件");
          return false;
        }

        const configText = await response.text();
        // 提取配置对象
        const configMatch = configText.match(
          /window\.\$config\s*=\s*(\{[\s\S]*\});/
        );
        if (configMatch && configMatch[1]) {
          try {
            // 安全地解析配置对象
            const configObj = this.safeParseConfig(configMatch[1]);
            if (configObj) {
              this.importFromConfigObject(configObj);
              return true;
            }
          } catch (parseError) {
            console.error("解析配置文件失败:", parseError);
          }
        }
      } catch (fetchError) {
        console.error("加载配置文件失败:", fetchError);
      }

      return false;
    } catch (error) {
      console.error("导入配置失败:", error);
      return false;
    }
  }

  // 从配置对象导入场景
  importFromConfigObject(config: any): void {
    if (!config || !config.scenes) {
      console.warn("配置对象中没有scenes属性");
      return;
    }

    // 清空现有场景
    this.scenes.clear();

    // 导入所有场景
    Object.entries(config.scenes).forEach(
      ([sceneId, sceneConfig]: [string, any]) => {
        const scene = this.createScene(
          sceneConfig.name || `场景 ${sceneId}`,
          sceneId,
          sceneConfig.models,
          sceneConfig.envTemplate?.$ref
            ? sceneConfig.envTemplate.$ref.split(".").pop()
            : "default",
          sceneConfig.scene || "DefaultScene"
        );

        // 生成对应的节点图
        const graphData = this.generateGraphFromSceneConfig(sceneConfig, sceneId);
        scene.graphData = graphData;

        // 如果是默认场景，设置为当前场景
        if (config.defaultScene === sceneId) {
          this.currentSceneId.value = scene.id;
        }
      }
    );

    // 如果没有设置当前场景，选择第一个场景
    if (!this.currentSceneId.value && this.getAllScenes().length > 0) {
      this.currentSceneId.value = this.getAllScenes()[0].id;
    }

    // 保存到本地存储
    this.saveToLocalStorage();
  }

  // 从场景配置生成节点图
  private generateGraphFromSceneConfig(sceneConfig: any, sceneId: string): any {
    const graphBuilder = new GraphBuilder();

    return graphBuilder
      .addSceneConfigNode(sceneConfig, sceneId)
      .addActionNodes(sceneConfig.actions || [])
      .addLifecycleNodes(sceneConfig.lifecycle || {})
      .build();
  }

  // 安全解析配置字符串
  private safeParseConfig(configStr: string): any {
    try {
      // 移除可能的危险代码
      const sanitized = configStr
        .replace(/function\s*\([^)]*\)\s*\{[^}]*\}/g, 'null')
        .replace(/new\s+\w+\([^)]*\)/g, 'null')
        .replace(/eval\s*\([^)]*\)/g, 'null')
        .replace(/setTimeout\s*\([^)]*\)/g, 'null')
        .replace(/setInterval\s*\([^)]*\)/g, 'null')
        .replace(/document\./g, 'null.')
        .replace(/window\./g, 'null.');

      // 尝试解析为JSON
      return JSON.parse(sanitized);
    } catch (error) {
      console.error('配置解析失败:', error);
      return null;
    }
  }

  // 初始化场景服务
  async initialize(): Promise<void> {
    // 先尝试从本地存储加载
    this.loadFromLocalStorage();

    // 如果没有场景，尝试从config.js导入
    if (this.getAllScenes().length === 0) {
      const imported = await this.importFromConfig();

      // 如果导入失败，创建默认场景
      if (!imported) {
        this.createScene("默认场景");
      }

      this.saveToLocalStorage();
    }
  }

  // 重置所有场景（清空并重新导入）
  async resetScenes(): Promise<void> {
    this.scenes.clear();
    this.currentSceneId.value = null;
    await this.importFromConfig();
    this.saveToLocalStorage();
  }
}

// GraphBuilder 类用于构建节点图
class GraphBuilder {
  private cells: any[] = []
  private nodeIdCounter = 1
  private currentY = 200
  private readonly nodeSpacing = 150

  addSceneConfigNode(sceneConfig: any, sceneId: string): this {
    const sceneConfigNode = {
      id: `scene-config-${this.nodeIdCounter++}`,
      shape: 'blueprint-node',
      x: 100,
      y: 50,
      data: {
        nodeType: 'scene-config',
        sceneName: sceneConfig.name || `场景 ${sceneId}`,
        models: sceneConfig.models || [],
        scene: sceneConfig.scene || 'DefaultScene',
        environment: 'techStyle',
        displayName: `场景: ${sceneConfig.name || sceneId}`
      },
      ports: [{ id: 'out-exec', group: 'exec-out' }]
    }
    this.cells.push(sceneConfigNode)
    return this
  }

  addActionNodes(actions: any[]): this {
    if (!Array.isArray(actions)) return this

    actions.forEach((action: any) => {
      const eventNodeId = `event-${this.nodeIdCounter++}`
      const eventNode = {
        id: eventNodeId,
        shape: 'blueprint-node',
        x: 100,
        y: this.currentY,
        data: {
          nodeType: 'event',
          actionType: action.actionType || 'doubleClick',
          meshNames: action.meshNames || [],
          displayName: `${action.actionType || '事件'}`,
          description: action.config?.description || `${action.actionType}事件`
        },
        ports: [
          { id: 'out-exec', group: 'exec-out' }
        ]
      }
      this.cells.push(eventNode)

      let nextX = 350
      let lastNodeId = eventNodeId

      // 生成高亮动作节点
      if (action.config?.highlight) {
        const highlightNodeId = `highlight-${this.nodeIdCounter++}`
        const highlightNode = {
          id: highlightNodeId,
          shape: 'blueprint-node',
          x: nextX,
          y: this.currentY,
          data: {
            nodeType: 'action-highlight',
            color: action.config.highlight.color || [1, 1, 0],
            duration: action.config.highlight.duration || 0,
            intensity: action.config.highlight.intensity || 1.2,
            enabled: action.config.highlight.enabled !== false,
            displayName: '高亮动作'
          },
          ports: [
            { id: 'in-exec', group: 'exec-in' },
            { id: 'out-exec', group: 'exec-out' }
          ]
        }
        this.cells.push(highlightNode)

        // 连接事件节点到高亮节点
        this.cells.push({
          id: `edge-${this.nodeIdCounter++}`,
          shape: 'edge',
          source: { cell: lastNodeId, port: 'out-exec' },
          target: { cell: highlightNodeId, port: 'in-exec' }
        })

        lastNodeId = highlightNodeId
        nextX += 200
      }

      // 生成回调动作节点
      if (action.config?.callback) {
        const callbackNodeId = `callback-${this.nodeIdCounter++}`
        const callbackNode = {
          id: callbackNodeId,
          shape: 'blueprint-node',
          x: nextX,
          y: this.currentY,
          data: {
            nodeType: 'action-callback',
            callback: action.config.callback,
            parameters: action.config.parameters || {},
            displayName: '回调动作'
          },
          ports: [
            { id: 'in-exec', group: 'exec-in' },
            { id: 'out-exec', group: 'exec-out' }
          ]
        }
        this.cells.push(callbackNode)

        // 连接到回调节点
        this.cells.push({
          id: `edge-${this.nodeIdCounter++}`,
          shape: 'edge',
          source: { cell: lastNodeId, port: 'out-exec' },
          target: { cell: callbackNodeId, port: 'in-exec' }
        })

        lastNodeId = callbackNodeId
      }

      this.currentY += this.nodeSpacing
    })

    return this
  }

  addLifecycleNodes(lifecycle: any): this {
    if (!lifecycle || typeof lifecycle !== 'object') return this

    Object.entries(lifecycle).forEach(([lifecycleType, callbacks]: [string, any]) => {
      if (Array.isArray(callbacks) && callbacks.length > 0) {
        const lifecycleNodeId = `lifecycle-${this.nodeIdCounter++}`
        const lifecycleNode = {
          id: lifecycleNodeId,
          shape: 'blueprint-node',
          x: 100,
          y: this.currentY,
          data: {
            nodeType: 'lifecycle',
            lifecycleType: lifecycleType,
            trigger: 'immediate',
            displayName: `生命周期: ${lifecycleType}`
          },
          ports: [{ id: 'out-exec', group: 'exec-out' }]
        }
        this.cells.push(lifecycleNode)

        this.currentY += this.nodeSpacing
      }
    })

    return this
  }

  build(): any {
    return { cells: this.cells }
  }
}

// 创建单例实例
export const sceneService = new SceneService();

// 自动初始化
sceneService.initialize();
