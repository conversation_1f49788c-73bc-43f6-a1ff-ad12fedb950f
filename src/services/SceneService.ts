import { reactive, ref } from "vue";

// 声明全局window.$config类型
declare global {
  interface Window {
    $config?: any;
  }
}

// 场景数据接口
export interface Scene {
  id: string;
  name: string;
  graphData: any;
  lastModified: number;
  models?: string[];
  environment?: string;
  scene?: string;
  configId?: string; // 对应config.js中的场景ID
}

class SceneService {
  private scenes = reactive<Map<string, Scene>>(new Map());
  private currentSceneId = ref<string | null>(null);

  // 获取所有场景
  getAllScenes(): Scene[] {
    return Array.from(this.scenes.values());
  }

  // 获取当前场景
  getCurrentScene(): Scene | null {
    if (!this.currentSceneId.value) return null;
    return this.scenes.get(this.currentSceneId.value) || null;
  }

  // 获取当前场景ID
  getCurrentSceneId(): string | null {
    return this.currentSceneId.value;
  }

  // 设置当前场景
  setCurrentScene(sceneId: string): void {
    if (this.scenes.has(sceneId)) {
      this.currentSceneId.value = sceneId;
    }
  }

  // 创建新场景
  createScene(
    name: string,
    configId?: string,
    models?: string[],
    environment?: string,
    scene?: string
  ): Scene {
    const id = `scene-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    const newScene: Scene = {
      id,
      name,
      graphData: null,
      lastModified: Date.now(),
      configId,
      models,
      environment,
      scene,
    };

    this.scenes.set(id, newScene);
    this.currentSceneId.value = id;
    return newScene;
  }

  // 更新场景
  updateScene(sceneId: string, data: Partial<Scene>): void {
    const scene = this.scenes.get(sceneId);
    if (scene) {
      Object.assign(scene, data, { lastModified: Date.now() });
    }
  }

  // 更新场景图数据
  updateGraphData(sceneId: string, graphData: any): void {
    const scene = this.scenes.get(sceneId);
    if (scene) {
      scene.graphData = graphData;
      scene.lastModified = Date.now();
    }
  }

  // 删除场景
  deleteScene(sceneId: string): boolean {
    if (!this.scenes.has(sceneId)) {
      return false;
    }

    const result = this.scenes.delete(sceneId);

    // 如果删除的是当前场景，切换到第一个场景
    if (result && this.currentSceneId.value === sceneId) {
      const firstScene = this.getAllScenes()[0];
      this.currentSceneId.value = firstScene ? firstScene.id : null;
    }

    return result;
  }

  // 保存所有场景到本地存储
  saveToLocalStorage(): void {
    const scenesArray = this.getAllScenes();
    localStorage.setItem("ddd-flow-scenes", JSON.stringify(scenesArray));
    localStorage.setItem(
      "ddd-flow-current-scene",
      this.currentSceneId.value || ""
    );
  }

  // 从本地存储加载场景
  loadFromLocalStorage(): void {
    try {
      const scenesJson = localStorage.getItem("ddd-flow-scenes");
      const currentSceneId = localStorage.getItem("ddd-flow-current-scene");

      if (scenesJson) {
        const scenesArray = JSON.parse(scenesJson) as Scene[];
        this.scenes.clear();

        scenesArray.forEach((scene) => {
          this.scenes.set(scene.id, scene);
        });

        if (currentSceneId && this.scenes.has(currentSceneId)) {
          this.currentSceneId.value = currentSceneId;
        } else if (scenesArray.length > 0) {
          this.currentSceneId.value = scenesArray[0].id;
        }
      }
    } catch (error) {
      console.error("Failed to load scenes from localStorage:", error);
    }
  }

  // 从config.js导入场景配置
  async importFromConfig(): Promise<boolean> {
    try {
      // 尝试从window对象获取配置
      if (typeof window !== "undefined" && window.$config) {
        this.importFromConfigObject(window.$config);
        return true;
      }

      // 如果window.$config不存在，尝试从文件加载
      try {
        const response = await fetch("/config/config.js");
        if (!response.ok) {
          console.warn("无法加载config.js文件");
          return false;
        }

        const configText = await response.text();
        // 提取配置对象
        const configMatch = configText.match(
          /window\.\$config\s*=\s*(\{[\s\S]*\});/
        );
        if (configMatch && configMatch[1]) {
          try {
            // 使用Function构造函数安全地解析JSON
            const configObj = new Function(`return ${configMatch[1]}`)();
            this.importFromConfigObject(configObj);
            return true;
          } catch (parseError) {
            console.error("解析配置文件失败:", parseError);
          }
        }
      } catch (fetchError) {
        console.error("加载配置文件失败:", fetchError);
      }

      return false;
    } catch (error) {
      console.error("导入配置失败:", error);
      return false;
    }
  }

  // 从配置对象导入场景
  importFromConfigObject(config: any): void {
    if (!config || !config.scenes) {
      console.warn("配置对象中没有scenes属性");
      return;
    }

    // 清空现有场景
    this.scenes.clear();

    // 导入所有场景
    Object.entries(config.scenes).forEach(
      ([sceneId, sceneConfig]: [string, any]) => {
        const scene = this.createScene(
          sceneConfig.name || `场景 ${sceneId}`,
          sceneId,
          sceneConfig.models,
          sceneConfig.envTemplate?.$ref
            ? sceneConfig.envTemplate.$ref.split(".").pop()
            : "default",
          sceneConfig.scene || "DefaultScene"
        );

        // 如果是默认场景，设置为当前场景
        if (config.defaultScene === sceneId) {
          this.currentSceneId.value = scene.id;
        }
      }
    );

    // 如果没有设置当前场景，选择第一个场景
    if (!this.currentSceneId.value && this.getAllScenes().length > 0) {
      this.currentSceneId.value = this.getAllScenes()[0].id;
    }

    // 保存到本地存储
    this.saveToLocalStorage();
  }

  // 初始化场景服务
  async initialize(): Promise<void> {
    // 先尝试从本地存储加载
    this.loadFromLocalStorage();

    // 如果没有场景，尝试从config.js导入
    if (this.getAllScenes().length === 0) {
      const imported = await this.importFromConfig();

      // 如果导入失败，创建默认场景
      if (!imported) {
        this.createScene("默认场景");
      }

      this.saveToLocalStorage();
    }
  }

  // 重置所有场景（清空并重新导入）
  async resetScenes(): Promise<void> {
    this.scenes.clear();
    this.currentSceneId.value = null;
    await this.importFromConfig();
    this.saveToLocalStorage();
  }
}

// 创建单例实例
export const sceneService = new SceneService();

// 自动初始化
sceneService.initialize();
