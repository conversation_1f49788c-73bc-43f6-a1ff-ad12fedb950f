export interface AppError {
  code: string
  message: string
  context: string
  severity: 'low' | 'medium' | 'high'
  timestamp: number
  originalError: Error
  userMessage?: string
}

export interface ErrorNotification {
  id: string
  message: string
  type: 'error' | 'warning' | 'info'
  duration?: number
  actions?: Array<{
    label: string
    action: () => void
  }>
}

export class ErrorHandler {
  private static instance: ErrorHandler
  private errorQueue: AppError[] = []
  private maxQueueSize = 50
  private notificationCallbacks: Array<(notification: ErrorNotification) => void> = []

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler()
    }
    return ErrorHandler.instance
  }

  /**
   * 处理错误
   */
  handleError(
    error: Error, 
    context: string, 
    severity: 'low' | 'medium' | 'high' = 'medium',
    userMessage?: string
  ): void {
    const appError: AppError = {
      code: this.generateErrorCode(error),
      message: error.message,
      context,
      severity,
      timestamp: Date.now(),
      originalError: error,
      userMessage: userMessage || this.getUserFriendlyMessage(error)
    }

    this.addToQueue(appError)
    this.logError(appError)
    this.notifyUser(appError)
    
    // 高严重性错误需要上报
    if (severity === 'high') {
      this.reportError(appError)
    }
  }

  /**
   * 添加通知回调
   */
  onNotification(callback: (notification: ErrorNotification) => void): void {
    this.notificationCallbacks.push(callback)
  }

  /**
   * 移除通知回调
   */
  removeNotification(callback: (notification: ErrorNotification) => void): void {
    const index = this.notificationCallbacks.indexOf(callback)
    if (index > -1) {
      this.notificationCallbacks.splice(index, 1)
    }
  }

  /**
   * 获取错误历史
   */
  getErrorHistory(): AppError[] {
    return [...this.errorQueue]
  }

  /**
   * 清空错误历史
   */
  clearErrorHistory(): void {
    this.errorQueue = []
  }

  /**
   * 生成错误代码
   */
  private generateErrorCode(error: Error): string {
    const errorType = error.constructor.name
    const timestamp = Date.now().toString(36)
    return `${errorType}_${timestamp}`
  }

  /**
   * 获取用户友好的错误消息
   */
  private getUserFriendlyMessage(error: Error): string {
    const errorMap: Record<string, string> = {
      'TypeError': '数据格式错误，请检查输入内容',
      'ReferenceError': '引用错误，请刷新页面重试',
      'SyntaxError': '配置文件格式错误，请检查语法',
      'NetworkError': '网络连接失败，请检查网络连接',
      'ValidationError': '数据验证失败，请检查配置信息',
      'PermissionError': '权限不足，请联系管理员',
      'TimeoutError': '操作超时，请重试',
      'NotFoundError': '资源未找到，请检查路径'
    }

    const errorType = error.constructor.name
    return errorMap[errorType] || '操作失败，请重试或联系技术支持'
  }

  /**
   * 添加到错误队列
   */
  private addToQueue(appError: AppError): void {
    this.errorQueue.unshift(appError)
    
    // 限制队列大小
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue = this.errorQueue.slice(0, this.maxQueueSize)
    }
  }

  /**
   * 记录错误日志
   */
  private logError(appError: AppError): void {
    const logLevel = this.getLogLevel(appError.severity)
    const logMessage = `[${appError.code}] ${appError.context}: ${appError.message}`
    
    switch (logLevel) {
      case 'error':
        console.error(logMessage, appError.originalError)
        break
      case 'warn':
        console.warn(logMessage, appError.originalError)
        break
      case 'info':
        console.info(logMessage, appError.originalError)
        break
    }
  }

  /**
   * 通知用户
   */
  private notifyUser(appError: AppError): void {
    const notification: ErrorNotification = {
      id: appError.code,
      message: appError.userMessage || appError.message,
      type: this.getNotificationType(appError.severity),
      duration: this.getNotificationDuration(appError.severity),
      actions: this.getNotificationActions(appError)
    }

    this.notificationCallbacks.forEach(callback => {
      try {
        callback(notification)
      } catch (error) {
        console.error('Error in notification callback:', error)
      }
    })
  }

  /**
   * 上报错误
   */
  private reportError(appError: AppError): void {
    // 这里可以实现错误上报逻辑
    // 例如发送到错误监控服务
    console.log('Reporting error:', appError)
  }

  /**
   * 获取日志级别
   */
  private getLogLevel(severity: string): 'error' | 'warn' | 'info' {
    switch (severity) {
      case 'high': return 'error'
      case 'medium': return 'warn'
      case 'low': return 'info'
      default: return 'warn'
    }
  }

  /**
   * 获取通知类型
   */
  private getNotificationType(severity: string): 'error' | 'warning' | 'info' {
    switch (severity) {
      case 'high': return 'error'
      case 'medium': return 'warning'
      case 'low': return 'info'
      default: return 'warning'
    }
  }

  /**
   * 获取通知持续时间
   */
  private getNotificationDuration(severity: string): number {
    switch (severity) {
      case 'high': return 0 // 不自动消失
      case 'medium': return 5000 // 5秒
      case 'low': return 3000 // 3秒
      default: return 4000
    }
  }

  /**
   * 获取通知操作
   */
  private getNotificationActions(appError: AppError): Array<{ label: string; action: () => void }> {
    const actions: Array<{ label: string; action: () => void }> = []

    // 高严重性错误提供重试选项
    if (appError.severity === 'high') {
      actions.push({
        label: '重试',
        action: () => {
          // 这里可以实现重试逻辑
          console.log('Retrying operation for error:', appError.code)
        }
      })
    }

    // 所有错误都提供查看详情选项
    actions.push({
      label: '查看详情',
      action: () => {
        console.log('Error details:', appError)
      }
    })

    return actions
  }
}

// 创建全局实例
export const errorHandler = ErrorHandler.getInstance()

// 全局错误处理
if (typeof window !== 'undefined') {
  window.addEventListener('error', (event) => {
    errorHandler.handleError(
      new Error(event.message),
      'Global Error Handler',
      'high',
      '页面发生未捕获的错误'
    )
  })

  window.addEventListener('unhandledrejection', (event) => {
    errorHandler.handleError(
      new Error(event.reason?.message || 'Promise rejection'),
      'Unhandled Promise Rejection',
      'high',
      'Promise 执行失败'
    )
  })
}
