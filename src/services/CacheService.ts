export interface CacheItem<T> {
  data: T
  timestamp: number
  ttl: number // Time to live in milliseconds
  accessCount: number
  lastAccessed: number
}

export interface CacheOptions {
  ttl?: number // Default TTL in milliseconds
  maxSize?: number // Maximum number of items
  cleanupInterval?: number // Cleanup interval in milliseconds
}

export class CacheService {
  private static instance: CacheService
  private cache: Map<string, CacheItem<any>> = new Map()
  private options: Required<CacheOptions>
  private cleanupTimer?: number

  private constructor(options: CacheOptions = {}) {
    this.options = {
      ttl: options.ttl || 5 * 60 * 1000, // 5 minutes default
      maxSize: options.maxSize || 100,
      cleanupInterval: options.cleanupInterval || 60 * 1000, // 1 minute
    }
    
    this.startCleanupTimer()
  }

  static getInstance(options?: CacheOptions): CacheService {
    if (!CacheService.instance) {
      CacheService.instance = new CacheService(options)
    }
    return CacheService.instance
  }

  /**
   * 设置缓存项
   */
  set<T>(key: string, data: T, ttl?: number): void {
    const now = Date.now()
    const item: CacheItem<T> = {
      data,
      timestamp: now,
      ttl: ttl || this.options.ttl,
      accessCount: 0,
      lastAccessed: now,
    }

    // 如果缓存已满，删除最少使用的项
    if (this.cache.size >= this.options.maxSize) {
      this.evictLeastUsed()
    }

    this.cache.set(key, item)
  }

  /**
   * 获取缓存项
   */
  get<T>(key: string): T | null {
    const item = this.cache.get(key)
    if (!item) {
      return null
    }

    const now = Date.now()
    
    // 检查是否过期
    if (now - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }

    // 更新访问统计
    item.accessCount++
    item.lastAccessed = now

    return item.data as T
  }

  /**
   * 检查缓存项是否存在且未过期
   */
  has(key: string): boolean {
    const item = this.cache.get(key)
    if (!item) {
      return false
    }

    const now = Date.now()
    if (now - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return false
    }

    return true
  }

  /**
   * 删除缓存项
   */
  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear()
  }

  /**
   * 获取或设置缓存项（如果不存在则创建）
   */
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T> | T,
    ttl?: number
  ): Promise<T> {
    const cached = this.get<T>(key)
    if (cached !== null) {
      return cached
    }

    const data = await factory()
    this.set(key, data, ttl)
    return data
  }

  /**
   * 批量获取缓存项
   */
  getMultiple<T>(keys: string[]): Map<string, T | null> {
    const result = new Map<string, T | null>()
    for (const key of keys) {
      result.set(key, this.get<T>(key))
    }
    return result
  }

  /**
   * 批量设置缓存项
   */
  setMultiple<T>(items: Map<string, T>, ttl?: number): void {
    for (const [key, data] of items) {
      this.set(key, data, ttl)
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): {
    size: number
    maxSize: number
    hitRate: number
    items: Array<{
      key: string
      size: number
      accessCount: number
      lastAccessed: number
      ttl: number
      isExpired: boolean
    }>
  } {
    const now = Date.now()
    const items: any[] = []
    let totalAccess = 0

    for (const [key, item] of this.cache) {
      const isExpired = now - item.timestamp > item.ttl
      items.push({
        key,
        size: this.estimateSize(item.data),
        accessCount: item.accessCount,
        lastAccessed: item.lastAccessed,
        ttl: item.ttl,
        isExpired,
      })
      totalAccess += item.accessCount
    }

    const hitRate = totalAccess > 0 ? (items.length / totalAccess) * 100 : 0

    return {
      size: this.cache.size,
      maxSize: this.options.maxSize,
      hitRate,
      items,
    }
  }

  /**
   * 清理过期项
   */
  cleanup(): number {
    const now = Date.now()
    let cleanedCount = 0

    for (const [key, item] of this.cache) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key)
        cleanedCount++
      }
    }

    return cleanedCount
  }

  /**
   * 驱逐最少使用的项
   */
  private evictLeastUsed(): void {
    let leastUsedKey: string | null = null
    let leastUsedCount = Infinity
    let oldestAccess = Infinity

    for (const [key, item] of this.cache) {
      if (item.accessCount < leastUsedCount || 
          (item.accessCount === leastUsedCount && item.lastAccessed < oldestAccess)) {
        leastUsedKey = key
        leastUsedCount = item.accessCount
        oldestAccess = item.lastAccessed
      }
    }

    if (leastUsedKey) {
      this.cache.delete(leastUsedKey)
    }
  }

  /**
   * 估算数据大小（简单实现）
   */
  private estimateSize(data: any): number {
    try {
      return JSON.stringify(data).length
    } catch {
      return 0
    }
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    if (typeof window !== 'undefined') {
      this.cleanupTimer = window.setInterval(() => {
        const cleaned = this.cleanup()
        if (cleaned > 0 && import.meta.env.DEV) {
          console.log(`🧹 Cache cleanup: removed ${cleaned} expired items`)
        }
      }, this.options.cleanupInterval)
    }
  }

  /**
   * 停止清理定时器
   */
  private stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = undefined
    }
  }

  /**
   * 销毁缓存服务
   */
  destroy(): void {
    this.stopCleanupTimer()
    this.clear()
  }
}

// 创建全局实例
export const cacheService = CacheService.getInstance({
  ttl: 10 * 60 * 1000, // 10 minutes
  maxSize: 200,
  cleanupInterval: 2 * 60 * 1000, // 2 minutes
})

// 缓存装饰器
export function cached(ttl?: number, keyGenerator?: (...args: any[]) => string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const cacheKey = keyGenerator 
        ? keyGenerator(...args)
        : `${target.constructor.name}.${propertyKey}:${JSON.stringify(args)}`

      return cacheService.getOrSet(cacheKey, () => originalMethod.apply(this, args), ttl)
    }

    return descriptor
  }
}
