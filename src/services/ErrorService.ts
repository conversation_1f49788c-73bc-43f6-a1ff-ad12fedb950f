import { errorHandler } from './ErrorHandler'
import type { AppError } from '../types'

export class ErrorService {
  private static instance: ErrorService

  private constructor() {}

  static getInstance(): ErrorService {
    if (!ErrorService.instance) {
      ErrorService.instance = new ErrorService()
    }
    return ErrorService.instance
  }

  /**
   * 处理应用错误 - 委托给新的ErrorHandler
   */
  handleError(error: Error | AppError, context: string = 'Unknown'): void {
    if (error instanceof Error) {
      errorHandler.handleError(error, context)
    } else {
      // 如果是AppError，转换为Error再处理
      const err = new Error(error.message)
      errorHandler.handleError(err, context)
    }
  }

  /**
   * 添加通知处理器 - 委托给ErrorHandler
   */
  addErrorHandler(handler: (error: AppError) => void): void {
    // 转换为ErrorHandler的通知格式
    errorHandler.onNotification((notification) => {
      const appError: AppError = {
        code: notification.id,
        message: notification.message,
        details: {},
        timestamp: Date.now()
      }
      handler(appError)
    })
  }

  /**
   * 移除错误处理器 - 暂不支持，建议直接使用ErrorHandler
   */
  removeErrorHandler(_handler: (error: AppError) => void): void {
    console.warn('removeErrorHandler is deprecated, use ErrorHandler directly')
  }

  /**
   * 创建应用错误
   */
  createError(code: string, message: string, details?: any): AppError {
    return {
      code,
      message,
      details,
      timestamp: Date.now(),
    }
  }

  /**
   * 异步错误处理包装器
   */
  async withErrorHandling<T>(
    operation: () => Promise<T>,
    context: string
  ): Promise<T | null> {
    try {
      return await operation()
    } catch (error) {
      this.handleError(error as Error, context)
      return null
    }
  }

  /**
   * 同步错误处理包装器
   */
  withSyncErrorHandling<T>(
    operation: () => T,
    context: string
  ): T | null {
    try {
      return operation()
    } catch (error) {
      this.handleError(error as Error, context)
      return null
    }
  }
}

// 创建全局实例
export const errorService = ErrorService.getInstance()

// 全局错误处理
if (typeof window !== 'undefined') {
  window.addEventListener('error', (event) => {
    errorService.handleError(event.error, 'Global Error Handler')
  })

  window.addEventListener('unhandledrejection', (event) => {
    errorService.handleError(
      new Error(event.reason),
      'Unhandled Promise Rejection'
    )
  })
}
