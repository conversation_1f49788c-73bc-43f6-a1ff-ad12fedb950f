import type { AppError } from '../types'

export class ErrorService {
  private static instance: ErrorService
  private errorHandlers: Array<(error: AppError) => void> = []

  private constructor() {}

  static getInstance(): ErrorService {
    if (!ErrorService.instance) {
      ErrorService.instance = new ErrorService()
    }
    return ErrorService.instance
  }

  /**
   * 处理应用错误
   */
  handleError(error: Error | AppError, context: string = 'Unknown'): void {
    const appError: AppError = this.normalizeError(error, context)
    
    // 记录错误
    console.error(`[${context}]`, appError)
    
    // 通知错误处理器
    this.errorHandlers.forEach(handler => {
      try {
        handler(appError)
      } catch (handlerError) {
        console.error('Error in error handler:', handlerError)
      }
    })

    // 在开发环境下显示更详细的错误信息
    if (import.meta.env.DEV) {
      this.showDevError(appError)
    }
  }

  /**
   * 添加错误处理器
   */
  addErrorHandler(handler: (error: AppError) => void): void {
    this.errorHandlers.push(handler)
  }

  /**
   * 移除错误处理器
   */
  removeErrorHandler(handler: (error: AppError) => void): void {
    const index = this.errorHandlers.indexOf(handler)
    if (index > -1) {
      this.errorHandlers.splice(index, 1)
    }
  }

  /**
   * 创建应用错误
   */
  createError(code: string, message: string, details?: any): AppError {
    return {
      code,
      message,
      details,
      timestamp: Date.now(),
    }
  }

  /**
   * 异步错误处理包装器
   */
  async withErrorHandling<T>(
    operation: () => Promise<T>,
    context: string
  ): Promise<T | null> {
    try {
      return await operation()
    } catch (error) {
      this.handleError(error as Error, context)
      return null
    }
  }

  /**
   * 同步错误处理包装器
   */
  withSyncErrorHandling<T>(
    operation: () => T,
    context: string
  ): T | null {
    try {
      return operation()
    } catch (error) {
      this.handleError(error as Error, context)
      return null
    }
  }

  private normalizeError(error: Error | AppError, context: string): AppError {
    if ('code' in error && 'timestamp' in error) {
      return error as AppError
    }

    return {
      code: 'UNKNOWN_ERROR',
      message: error.message || 'An unknown error occurred',
      details: {
        stack: error.stack,
        name: error.name,
        context,
      },
      timestamp: Date.now(),
    }
  }

  private showDevError(error: AppError): void {
    // 在开发环境下可以显示更友好的错误提示
    if (typeof window !== 'undefined' && window.console) {
      console.group(`🚨 Application Error [${error.code}]`)
      console.error('Message:', error.message)
      console.error('Timestamp:', new Date(error.timestamp).toISOString())
      if (error.details) {
        console.error('Details:', error.details)
      }
      console.groupEnd()
    }
  }
}

// 创建全局实例
export const errorService = ErrorService.getInstance()

// 全局错误处理
if (typeof window !== 'undefined') {
  window.addEventListener('error', (event) => {
    errorService.handleError(event.error, 'Global Error Handler')
  })

  window.addEventListener('unhandledrejection', (event) => {
    errorService.handleError(
      new Error(event.reason),
      'Unhandled Promise Rejection'
    )
  })
}
