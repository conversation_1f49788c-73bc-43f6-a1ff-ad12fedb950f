import { describe, it, expect, beforeEach, vi } from 'vitest'
import { SceneService } from '../../services/SceneService'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

describe('SceneService', () => {
  let sceneService: SceneService

  beforeEach(() => {
    sceneService = new SceneService()
    localStorageMock.getItem.mockClear()
    localStorageMock.setItem.mockClear()
  })

  describe('createScene', () => {
    it('should create a new scene with correct properties', () => {
      const scene = sceneService.createScene('测试场景', 'test-config')
      
      expect(scene.name).toBe('测试场景')
      expect(scene.configId).toBe('test-config')
      expect(scene.graphData).toBeNull()
      expect(scene.lastModified).toBeTypeOf('number')
      expect(scene.id).toMatch(/^scene-\d+-\d+$/)
    })

    it('should set created scene as current scene', () => {
      const scene = sceneService.createScene('测试场景')
      expect(sceneService.getCurrentSceneId()).toBe(scene.id)
    })
  })

  describe('updateScene', () => {
    it('should update scene properties', () => {
      const scene = sceneService.createScene('原始名称')
      const originalModified = scene.lastModified
      
      // Wait a bit to ensure timestamp changes
      setTimeout(() => {
        sceneService.updateScene(scene.id, { name: '更新名称' })
        
        const updatedScene = sceneService.getScene(scene.id)
        expect(updatedScene?.name).toBe('更新名称')
        expect(updatedScene?.lastModified).toBeGreaterThan(originalModified)
      }, 10)
    })
  })

  describe('deleteScene', () => {
    it('should delete scene and update current scene', () => {
      const scene1 = sceneService.createScene('场景1')
      const scene2 = sceneService.createScene('场景2')
      
      sceneService.deleteScene(scene2.id)
      
      expect(sceneService.getScene(scene2.id)).toBeUndefined()
      expect(sceneService.getCurrentSceneId()).toBe(scene1.id)
    })

    it('should clear current scene if last scene is deleted', () => {
      const scene = sceneService.createScene('唯一场景')
      
      sceneService.deleteScene(scene.id)
      
      expect(sceneService.getCurrentSceneId()).toBeNull()
    })
  })

  describe('getAllScenes', () => {
    it('should return all scenes sorted by last modified', () => {
      const scene1 = sceneService.createScene('场景1')
      setTimeout(() => {
        const scene2 = sceneService.createScene('场景2')
        
        const scenes = sceneService.getAllScenes()
        expect(scenes).toHaveLength(2)
        expect(scenes[0].id).toBe(scene2.id) // Most recent first
        expect(scenes[1].id).toBe(scene1.id)
      }, 10)
    })
  })

  describe('updateGraphData', () => {
    it('should update scene graph data', () => {
      const scene = sceneService.createScene('测试场景')
      const graphData = { nodes: [], edges: [] }
      
      sceneService.updateGraphData(scene.id, graphData)
      
      const updatedScene = sceneService.getScene(scene.id)
      expect(updatedScene?.graphData).toEqual(graphData)
    })
  })
})
