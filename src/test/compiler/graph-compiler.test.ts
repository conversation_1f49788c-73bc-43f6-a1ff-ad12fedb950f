import { describe, it, expect } from 'vitest'
import { compileGraph, compileMultipleGraphs } from '../../compiler/graph-compiler'

describe('GraphCompiler', () => {
  describe('compileGraph', () => {
    it('should return empty config for null input', () => {
      const result = compileGraph(null)
      expect(result).toEqual({})
    })

    it('should return empty config for empty graph data', () => {
      const result = compileGraph({})
      expect(result).toEqual({})
    })

    it('should compile scene config node correctly', () => {
      const graphData = {
        cells: [
          {
            id: 'scene-1',
            shape: 'node',
            data: {
              nodeType: 'scene-config',
              sceneName: '测试场景',
              models: ['test.glb'],
              scene: 'TestScene'
            }
          }
        ]
      }

      const result = compileGraph(graphData)
      
      expect(result.name).toBe('测试场景')
      expect(result.models).toEqual(['test.glb'])
      expect(result.scene).toBe('TestScene')
      expect(result.actions).toEqual([])
    })

    it('should compile event node with actions', () => {
      const graphData = {
        cells: [
          {
            id: 'event-1',
            shape: 'node',
            data: {
              nodeType: 'event',
              actionType: 'click',
              meshNames: ['test-mesh'],
              description: '点击事件'
            }
          },
          {
            id: 'action-1',
            shape: 'node',
            data: {
              nodeType: 'action-highlight',
              color: [1, 0, 0],
              duration: 1000,
              intensity: 1.5
            }
          },
          {
            id: 'edge-1',
            shape: 'edge',
            source: { cell: 'event-1', port: 'out-exec' },
            target: { cell: 'action-1', port: 'in-exec' }
          }
        ]
      }

      const result = compileGraph(graphData)
      
      expect(result.actions).toHaveLength(1)
      expect(result.actions[0].actionType).toBe('click')
      expect(result.actions[0].meshNames).toEqual(['test-mesh'])
      expect(result.actions[0].config.highlight.color).toEqual([1, 0, 0])
    })
  })

  describe('compileMultipleGraphs', () => {
    it('should compile multiple scenes correctly', () => {
      const graphsData = {
        'scene1': {
          cells: [
            {
              id: 'scene-1',
              shape: 'node',
              data: {
                nodeType: 'scene-config',
                sceneName: '场景1',
                models: ['scene1.glb']
              }
            }
          ]
        },
        'scene2': {
          cells: [
            {
              id: 'scene-2',
              shape: 'node',
              data: {
                nodeType: 'scene-config',
                sceneName: '场景2',
                models: ['scene2.glb']
              }
            }
          ]
        }
      }

      const result = compileMultipleGraphs(graphsData, 'scene1')
      
      expect(result.scenes).toBeDefined()
      expect(result.scenes!['scene1']).toBeDefined()
      expect(result.scenes!['scene2']).toBeDefined()
      expect(result.defaultScene).toBe('scene1')
      expect(result.templates).toBeDefined()
    })
  })
})
