<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>节点连接系统测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #e0e0e0;
            font-family: Arial, sans-serif;
        }
        .test-info {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-info h1 {
            color: #4CAF50;
            margin-top: 0;
        }
        .test-steps {
            background: #333;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .test-steps h3 {
            color: #1890ff;
            margin-top: 0;
        }
        .test-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .test-steps li {
            margin: 8px 0;
            line-height: 1.5;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background: #1B5E20;
            color: #4CAF50;
        }
        .status.error {
            background: #B71C1C;
            color: #F44336;
        }
        .port-legend {
            display: flex;
            gap: 20px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        .port-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .port-icon {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid;
        }
        .port-icon.exec {
            background: #404040;
            border-color: #ffffff;
        }
        .port-icon.data {
            background: #0050b3;
            border-color: #1890ff;
        }
        .port-icon.true {
            background: #1B5E20;
            border-color: #4CAF50;
        }
        .port-icon.false {
            background: #B71C1C;
            border-color: #F44336;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h1>🎯 节点连接系统修复验证</h1>
        <p>本页面用于验证DDD-Flow编辑器中节点连接系统的修复情况。</p>
        
        <div class="status success">
            ✅ 端口选择器冲突问题已修复
        </div>
        
        <div class="test-steps">
            <h3>修复的问题</h3>
            <ol>
                <li><strong>端口选择器冲突</strong>：所有端口组使用相同的选择器名称（circle, text）导致 "Selectors within port must be unique" 错误</li>
                <li><strong>端口不可见</strong>：端口被设置为隐藏状态</li>
                <li><strong>交互体验差</strong>：缺乏悬停效果和视觉反馈</li>
            </ol>
        </div>
        
        <div class="test-steps">
            <h3>修复方案</h3>
            <ol>
                <li><strong>唯一选择器</strong>：为每个端口组分配唯一的选择器名称：
                    <ul>
                        <li>exec-in: execInCircle, execInText</li>
                        <li>exec-out: execOutCircle, execOutText</li>
                        <li>data-in: dataInCircle, dataInText</li>
                        <li>data-out: dataOutCircle, dataOutText</li>
                        <li>logic-branch: branchInCircle, trueOutCircle, falseOutCircle</li>
                    </ul>
                </li>
                <li><strong>端口可见性</strong>：移除 visibility: 'hidden' 设置</li>
                <li><strong>交互效果</strong>：实现端口悬停放大和标签显示</li>
                <li><strong>连接验证</strong>：智能连接类型验证</li>
            </ol>
        </div>
        
        <div class="test-steps">
            <h3>端口类型说明</h3>
            <div class="port-legend">
                <div class="port-item">
                    <div class="port-icon exec"></div>
                    <span>执行流端口（白色）</span>
                </div>
                <div class="port-item">
                    <div class="port-icon data"></div>
                    <span>数据流端口（蓝色）</span>
                </div>
                <div class="port-item">
                    <div class="port-icon true"></div>
                    <span>True输出（绿色）</span>
                </div>
                <div class="port-item">
                    <div class="port-icon false"></div>
                    <span>False输出（红色）</span>
                </div>
            </div>
        </div>
        
        <div class="test-steps">
            <h3>测试步骤</h3>
            <ol>
                <li>打开主应用页面：<a href="/" style="color: #1890ff;">http://localhost:3001/</a></li>
                <li>在左侧节点库中点击添加不同类型的节点</li>
                <li>检查节点是否显示端口（应该可见）</li>
                <li>鼠标悬停在端口上查看放大效果和标签</li>
                <li>尝试从输出端口拖拽到输入端口创建连接</li>
                <li>验证连接验证规则（执行流只能连接执行流等）</li>
                <li>检查浏览器控制台是否还有错误信息</li>
            </ol>
        </div>
        
        <div class="test-steps">
            <h3>预期结果</h3>
            <ol>
                <li>✅ 不再出现 "Selectors within port must be unique" 错误</li>
                <li>✅ 所有节点的端口都清晰可见</li>
                <li>✅ 端口悬停时有放大和发光效果</li>
                <li>✅ 端口悬停时显示类型标签</li>
                <li>✅ 可以正常创建节点间的连接</li>
                <li>✅ 连接线根据类型显示不同颜色</li>
                <li>✅ 连接验证规则正常工作</li>
            </ol>
        </div>
    </div>
    
    <script>
        // 检查控制台错误
        let errorCount = 0;
        const originalError = console.error;
        console.error = function(...args) {
            errorCount++;
            originalError.apply(console, args);
            
            // 检查是否是端口选择器错误
            const message = args.join(' ');
            if (message.includes('Selectors within port must be unique')) {
                document.body.innerHTML += `
                    <div class="status error">
                        ❌ 仍然存在端口选择器冲突错误！
                    </div>
                `;
            }
        };
        
        // 5秒后检查错误计数
        setTimeout(() => {
            if (errorCount === 0) {
                document.body.innerHTML += `
                    <div class="status success">
                        🎉 太好了！没有检测到端口选择器错误！
                    </div>
                `;
            } else {
                document.body.innerHTML += `
                    <div class="status error">
                        ⚠️ 检测到 ${errorCount} 个控制台错误，请检查修复情况。
                    </div>
                `;
            }
        }, 5000);
    </script>
</body>
</html>
