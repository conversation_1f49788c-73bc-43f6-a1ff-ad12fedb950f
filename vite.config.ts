import vue from "@vitejs/plugin-vue";
import { defineConfig } from "vite";
import { resolve } from "path";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      "@": resolve(__dirname, "./src"),
    },
  },
  build: {
    // 代码分割配置
    rollupOptions: {
      output: {
        manualChunks: {
          // 将 Vue 相关库分离到单独的 chunk
          vue: ["vue"],
          // 将 AntV X6 相关库分离到单独的 chunk
          x6: ["@antv/x6", "@antv/x6-plugin-minimap", "@antv/x6-vue-shape"],
          // 将服务层分离到单独的 chunk
          services: [
            "./src/services/ErrorService",
            "./src/services/PerformanceService",
            "./src/services/CacheService",
            "./src/services/UndoRedoService",
            "./src/services/KeyboardService",
            "./src/services/SceneService",
          ],
          // 将编译器分离到单独的 chunk
          compiler: ["./src/compiler/graph-compiler"],
        },
      },
    },
    // 启用源码映射以便调试
    sourcemap: true,
    // 设置 chunk 大小警告限制
    chunkSizeWarningLimit: 1000,
    // 启用 CSS 代码分割
    cssCodeSplit: true,
  },
  // 开发服务器配置
  server: {
    port: 3000,
    open: true,
    cors: true,
  },
  // 预览服务器配置
  preview: {
    port: 4173,
    open: true,
  },
  // 优化配置
  optimizeDeps: {
    include: [
      "vue",
      "@antv/x6",
      "@antv/x6-plugin-minimap",
      "@antv/x6-vue-shape",
    ],
  },
});
