# ddd-flow 项目开发与实施计划文档

## 概述

本文档详细描述了 ddd-flow 项目的开发历程和下一阶段的实施计划，包括已完成功能、当前进展、未来任务、技术细节和实施时间表。ddd-flow 是一个可视化场景编辑器，用于创建和编辑 3D 场景的交互配置，允许用户通过可视化节点图构建复杂的交互逻辑，而无需直接编写代码。

## 项目历程

### 阶段一：概念验证 (PoC) 阶段 (已完成)

**目标:** 用最少的代码实现一个从可视化节点图到 `config.js` 格式配置文件的完整流程。

**核心任务:**

- [x] **任务 1.1: 拆分动作节点**

  - 创建 `CallbackActionNode`，专门用于表示服务函数调用
  - 创建 `HighlightActionNode`，专门用于表示高亮效果

- [x] **任务 1.2: 重写图编译算法**

  - 实现图遍历算法，从 `EventNode` 出发，找到所有下游连接的动作节点
  - 实现配置聚合，将节点信息填充到配置对象中

- [x] **任务 1.3: 验证输出**
  - 确保生成的配置与 `config.js` 格式完全匹配

**成果:** 成功完成了 PoC 验证，证明了从可视化节点图到 `config.js` 配置的核心流程是可行的。

### 阶段二：从 PoC 到生产力工具 (已完成)

**目标:** 将 ddd-flow 从基本功能验证项目升级为具备核心生产能力的低代码工具。

**完成的模块:**

1. **增强的属性面板**

   - [x] 创建动态属性面板组件
   - [x] 实现节点数据双向绑定

2. **完整的节点库**

   - [x] 扩展事件节点 (OnHover, OnClick, OnRightDoubleClick, OnHotkey 等)
   - [x] 扩展动作节点 (PlayAnimationNode, ToggleAnimationNode, ShowMessageNode 等)
   - [x] 引入逻辑控制节点 (BranchNode, SequenceNode)
   - [x] 实现节点库面板

3. **完善的编译与生成能力**

   - [x] 升级编译器以支持分支逻辑
   - [x] 实现完整的场景配置生成

4. **交互与体验优化**

   - [x] 添加右键上下文菜单
   - [x] 视觉与交互打磨

5. **问题修复与优化**

   - [x] 依赖管理与模块导入修复
   - [x] 性能优化

6. **数据驱动功能**

   - [x] 数据源节点 (轮询、WebSocket)
   - [x] 数据转换节点 (映射转换、范围转换)
   - [x] 数据映射节点 (CSV 映射文件)
   - [x] 数据消费节点 (切换可见性、动画控制、属性控制)

7. **生命周期事件功能**
   - [x] 场景激活(onActivated)事件
   - [x] 场景退出(onDeactivated)事件
   - [x] 模型加载完成(onModelLoaded)事件
   - [x] 场景初始化(onInit)事件

## 当前项目状态

ddd-flow 已经从一个简单的概念验证项目发展成为一个完整的低代码平台，具备以下核心能力：

1. **直观的可视化编辑**: 通过节点和连线可视化表达交互逻辑
2. **丰富的节点类型**: 支持事件、动作、逻辑控制等多种节点类型
3. **强大的编译能力**: 能将节点图编译成标准配置格式
4. **场景配置生成**: 支持生成完整的 3D 场景配置
5. **良好的用户体验**: 右键菜单、节点样式、小地图等增强功能
6. **数据驱动能力**: 支持数据源、转换、映射和消费的完整数据流
7. **生命周期管理**: 支持场景生命周期事件的配置

## 下一步开发任务详细说明

### 1. 模板引用机制实现

#### 1.1 引用节点实现

**目标**：创建 ReferenceNode 节点类型，支持引用已定义的模板。

**技术细节**：

1. **节点数据结构**：

```typescript
interface ReferenceNodeData {
  nodeType: "reference";
  refPath: string; // 引用路径，如 'templates.1.cameras.default'
  description?: string;
  parameters?: Record<string, any>; // 覆盖参数
}
```

2. **模板路径选择器组件**：

   - 创建 `TemplatePathSelector.vue` 组件
   - 实现路径树形展示
   - 支持搜索和过滤功能
   - 提供路径预览功能

3. **属性面板扩展**：

   - 在 `PropertiesPanel.vue` 中添加引用节点专用编辑区域
   - 支持选择引用路径
   - 支持编辑覆盖参数

4. **编译器支持**：
   - 在 `graph-compiler.ts` 中添加引用节点处理逻辑
   - 实现引用路径解析和参数合并

**实现步骤**：

1. 创建 ReferenceNode 节点类型和相关接口
2. 开发模板路径选择器组件
3. 扩展属性面板以支持引用节点编辑
4. 修改编译器以处理引用节点

**预计工作量**：3 天

#### 1.2 模板定义节点

**目标**：创建 TemplateNode 节点类型，允许将配置定义为模板。

**技术细节**：

1. **节点数据结构**：

```typescript
interface TemplateNodeData {
  nodeType: "template";
  templateId: string; // 模板ID，如 'cameras.default'
  templateType: "camera" | "style" | "action" | "label" | "environment";
  content: any; // 模板内容
  parameters?: Array<{
    name: string;
    type: "string" | "number" | "boolean" | "array" | "object";
    default?: any;
    description?: string;
  }>;
}
```

2. **模板编辑器组件**：

   - 创建 `TemplateEditor.vue` 组件
   - 支持模板内容编辑
   - 支持参数定义和管理
   - 提供模板预览功能

3. **属性面板扩展**：

   - 在 `PropertiesPanel.vue` 中添加模板节点专用编辑区域
   - 支持编辑模板 ID 和类型
   - 集成模板编辑器组件

4. **模板导出和导入功能**：
   - 实现模板导出为 JSON 文件
   - 支持从 JSON 文件导入模板
   - 添加模板库管理功能

**实现步骤**：

1. 创建 TemplateNode 节点类型和相关接口
2. 开发模板编辑器组件
3. 扩展属性面板以支持模板节点编辑
4. 实现模板导出和导入功能

**预计工作量**：2 天

#### 1.3 编译器支持

**目标**：扩展编译器以支持模板引用和定义。

**技术细节**：

1. **引用解析逻辑**：

   - 在 `compileGraph` 函数中添加引用节点处理
   - 实现 `$ref` 语法的生成
   - 处理参数覆盖和合并

2. **模板定义处理**：

   - 将模板节点编译为 `templates` 部分
   - 处理模板参数化
   - 确保模板 ID 的唯一性

3. **依赖关系处理**：
   - 检测和处理模板间的依赖关系
   - 避免循环引用
   - 优化模板组织结构

**实现步骤**：

1. 扩展 `compileGraph` 函数以处理引用节点
2. 实现模板节点的编译逻辑
3. 添加依赖关系检测和处理
4. 进行集成测试

**预计工作量**：2 天

### 2. 场景设置节点增强

#### 2.1 扩展场景配置节点的生命周期支持

**目标**：在 SceneConfigNode 中添加生命周期配置部分。

**技术细节**：

1. **节点数据扩展**：

```typescript
interface SceneConfigNodeData {
  // 现有字段
  nodeType: "scene-config";
  sceneId: string;
  sceneName: string;
  models: string[];
  environment: string;

  // 新增字段
  lifecycle?: {
    onActivated?: Array<{
      trigger: "immediate" | "delayed";
      delay?: number;
      callback?: string;
      parameters?: Record<string, any>;
    }>;
    onDeactivated?: Array<any>;
    onModelLoaded?: Array<any>;
    onInit?: Array<any>;
  };
}
```

2. **生命周期配置界面**：

   - 在属性面板中添加生命周期配置区域
   - 支持添加、编辑和删除生命周期事件
   - 提供常用生命周期事件模板

3. **编译器支持**：
   - 扩展 `compileGraph` 函数以处理场景节点的生命周期配置
   - 生成符合 config.js 格式的生命周期配置

**实现步骤**：

1. 扩展 SceneConfigNode 数据结构
2. 在属性面板中添加生命周期配置界面
3. 修改编译器以处理生命周期配置
4. 测试生命周期配置的编译结果

**预计工作量**：3 天

#### 2.2 场景配置模板预设

**目标**：添加常用场景配置模板，支持快速应用和从模板创建新场景。

**技术细节**：

1. **预设模板定义**：

```typescript
interface SceneTemplate {
  id: string;
  name: string;
  description: string;
  config: {
    models: string[];
    environment: string;
    camera: any;
    staticLabels?: any[];
    lifecycle?: any;
  };
}
```

2. **模板管理功能**：

   - 创建 `SceneTemplateManager.vue` 组件
   - 支持模板浏览和预览
   - 提供模板应用和创建功能

3. **场景创建流程扩展**：
   - 在 `SceneManager.vue` 中添加从模板创建场景的选项
   - 支持模板参数自定义
   - 实现模板应用到现有场景的功能

**实现步骤**：

1. 定义常用场景配置模板
2. 开发模板管理组件
3. 扩展场景创建流程
4. 测试从模板创建和应用场景

**预计工作量**：2 天

### 3. 高级属性面板优化

#### 3.1 复杂结构编辑器

**目标**：实现嵌套对象和数组的可视化编辑。

**技术细节**：

1. **组件设计**：

   - 创建 `ObjectEditor.vue` 组件，支持嵌套对象编辑
   - 创建 `ArrayEditor.vue` 组件，支持数组编辑
   - 实现折叠/展开机制
   - 支持拖拽排序

2. **数据绑定**：

   - 实现双向数据绑定
   - 支持深层次路径访问和修改
   - 处理复杂数据类型的验证

3. **用户体验优化**：
   - 添加字段提示和说明
   - 实现自动保存和撤销功能
   - 提供数据格式化选项

**实现步骤**：

1. 开发对象编辑器组件
2. 开发数组编辑器组件
3. 实现折叠/展开和拖拽排序功能
4. 集成到属性面板中

**预计工作量**：3 天

#### 3.2 专用控件开发

**目标**：实现颜色选择器、向量编辑器等专用控件。

**技术细节**：

1. **颜色选择器**：

   - 创建 `ColorPicker.vue` 组件
   - 支持 RGB 和 HSL 颜色模式
   - 提供颜色预览和历史记录

2. **向量编辑器**：

   - 创建 `VectorEditor.vue` 组件
   - 支持 2D 和 3D 向量编辑
   - 提供可视化预览

3. **文件选择器**：

   - 创建 `FileSelector.vue` 组件
   - 支持浏览项目文件
   - 提供文件预览功能

4. **路径选择器**：
   - 创建 `PathSelector.vue` 组件
   - 支持配置路径浏览和选择
   - 提供路径自动补全功能

**实现步骤**：

1. 开发颜色选择器组件
2. 开发向量编辑器组件
3. 开发文件选择器组件
4. 开发路径选择器组件
5. 集成到属性面板中

**预计工作量**：2 天

### 4. 特殊配置节点

#### 4.1 相机配置节点

**目标**：创建 CameraConfigNode 节点类型，实现相机参数的可视化编辑。

**技术细节**：

1. **节点数据结构**：

```typescript
interface CameraConfigNodeData {
  nodeType: "camera-config";
  cameraId: string;
  cameraType: "free" | "arc" | "follow";
  position?: [number, number, number];
  target?: [number, number, number];
  alpha?: number;
  beta?: number;
  radius?: number;
  animations?: any[];
}
```

2. **相机参数编辑界面**：

   - 在属性面板中添加相机参数编辑区域
   - 支持不同相机类型的参数配置
   - 提供相机预设选项

3. **编译器支持**：
   - 扩展 `compileGraph` 函数以处理相机配置节点
   - 生成符合 config.js 格式的相机配置

**实现步骤**：

1. 创建 CameraConfigNode 节点类型和相关接口
2. 开发相机参数编辑界面
3. 修改编译器以处理相机配置节点
4. 测试相机配置的编译结果

**预计工作量**：2 天

#### 4.2 高亮效果节点

**目标**：创建 BlinkingEffectNode 节点类型，支持闪烁参数配置。

**技术细节**：

1. **节点数据结构**：

```typescript
interface BlinkingEffectNodeData {
  nodeType: "blinking-effect";
  effectId: string;
  color: [number, number, number];
  intensity: number;
  duration: number;
  frequency: number;
  fadeIn: boolean;
  fadeOut: boolean;
}
```

2. **效果参数编辑界面**：

   - 在属性面板中添加闪烁效果参数编辑区域
   - 支持颜色、强度、频率等参数配置
   - 提供效果预览功能

3. **编译器支持**：
   - 扩展 `compileGraph` 函数以处理高亮效果节点
   - 生成符合 config.js 格式的效果配置

**实现步骤**：

1. 创建 BlinkingEffectNode 节点类型和相关接口
2. 开发效果参数编辑界面
3. 修改编译器以处理高亮效果节点
4. 测试效果配置的编译结果

**预计工作量**：1 天

#### 4.3 UI 样式节点

**目标**：创建 StyleConfigNode 节点类型，支持 UI 样式的可视化配置。

**技术细节**：

1. **节点数据结构**：

```typescript
interface StyleConfigNodeData {
  nodeType: "style-config";
  styleId: string;
  styleType: "label" | "panel" | "button";
  fontFamily?: string;
  fontSize?: number;
  fontColor?: string;
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: number;
  borderRadius?: number;
  padding?: [number, number, number, number];
  margin?: [number, number, number, number];
}
```

2. **样式参数编辑界面**：

   - 在属性面板中添加样式参数编辑区域
   - 支持字体、颜色、边框等参数配置
   - 提供样式预览功能

3. **编译器支持**：
   - 扩展 `compileGraph` 函数以处理样式配置节点
   - 生成符合 config.js 格式的样式配置

**实现步骤**：

1. 创建 StyleConfigNode 节点类型和相关接口
2. 开发样式参数编辑界面
3. 修改编译器以处理样式配置节点
4. 测试样式配置的编译结果

**预计工作量**：2 天

## 实施时间表

### 阶段一：模板引用机制（2 周）

| 日期 | 任务                                 | 负责人   |
| ---- | ------------------------------------ | -------- |
| 周一 | 设计引用节点的数据结构和接口         | 开发者 A |
| 周二 | 实现模板路径选择器组件               | 开发者 A |
| 周三 | 在属性面板中添加引用路径编辑功能     | 开发者 A |
| 周四 | 设计模板节点的数据结构和接口         | 开发者 B |
| 周五 | 实现模板参数化功能和预览组件         | 开发者 B |
| 周六 | 扩展编译器以支持引用解析             | 开发者 A |
| 周日 | 实现模板导出和导入功能，进行集成测试 | 开发者 B |

### 阶段二：场景设置节点增强（1 周）

| 日期 | 任务                                         | 负责人   |
| ---- | -------------------------------------------- | -------- |
| 周一 | 扩展场景配置节点以支持生命周期设置           | 开发者 A |
| 周二 | 在属性面板中添加生命周期配置界面             | 开发者 A |
| 周三 | 更新编译器以处理生命周期配置                 | 开发者 A |
| 周四 | 设计和实现场景配置模板                       | 开发者 B |
| 周五 | 开发模板快速应用功能和从模板创建新场景的功能 | 开发者 B |

### 阶段三：高级属性面板和特殊节点（2 周）

| 日期 | 任务                            | 负责人   |
| ---- | ------------------------------- | -------- |
| 周一 | 实现嵌套对象编辑器组件          | 开发者 A |
| 周二 | 实现数组编辑器组件              | 开发者 A |
| 周三 | 添加折叠/展开机制和拖拽排序功能 | 开发者 A |
| 周四 | 实现颜色选择器和向量编辑器      | 开发者 B |
| 周五 | 开发文件选择器和路径选择器      | 开发者 B |
| 周六 | 实现相机配置节点和高亮效果节点  | 开发者 A |
| 周日 | 创建 UI 样式节点和进行集成测试  | 开发者 B |

## 风险评估与缓解措施

1. **技术风险**：

   - **风险**：复杂数据结构的编辑可能导致性能问题
   - **缓解**：实现虚拟滚动和懒加载，优化大型数据结构的渲染

2. **集成风险**：

   - **风险**：新功能可能与现有功能冲突
   - **缓解**：增加单元测试覆盖率，实施持续集成

3. **用户体验风险**：
   - **风险**：复杂功能可能增加学习成本
   - **缓解**：提供详细文档和教程，实现渐进式功能引导

## 验收标准

1. 所有新功能都能正常工作，无明显 bug
2. 编译器能正确处理新节点类型，生成符合要求的配置
3. 用户界面响应迅速，操作流畅
4. 新功能有完整的文档和示例

## 未来展望

完成本阶段开发后，ddd-flow 将成为一个功能完备的低代码平台，能够满足复杂场景配置的需求。未来可考虑实现以下功能：

1. **实时预览功能**：提供配置效果的即时反馈
2. **版本控制与协作**：支持团队协作和配置版本管理
3. **性能优化**：提升大型场景的编辑体验
4. **扩展插件系统**：支持第三方功能扩展
