# DDD-Flow API 文档

## 概述

DDD-Flow 是一个基于领域驱动设计（DDD）原则的可视化场景编辑器，提供了丰富的 API 来支持节点图编辑、场景管理、配置编译等功能。

## 核心服务

### SceneService

场景管理服务，负责场景的创建、更新、删除和持久化。

```typescript
import { sceneService } from '@/services/SceneService'

// 创建新场景
const scene = sceneService.createScene('场景名称', 'config-id')

// 获取当前场景
const currentScene = sceneService.getCurrentScene()

// 更新场景数据
sceneService.updateScene(sceneId, { name: '新名称' })

// 删除场景
sceneService.deleteScene(sceneId)

// 获取所有场景
const allScenes = sceneService.getAllScenes()
```

### ErrorService

全局错误处理服务，提供统一的错误处理和日志记录。

```typescript
import { errorService } from '@/services/ErrorService'

// 处理错误
errorService.handleError(error, 'Context')

// 添加错误处理器
errorService.addErrorHandler((error) => {
  console.log('处理错误:', error)
})

// 创建应用错误
const appError = errorService.createError('ERROR_CODE', '错误消息')

// 异步错误处理包装器
const result = await errorService.withErrorHandling(async () => {
  // 可能抛出错误的操作
  return await someAsyncOperation()
}, 'Operation Context')
```

### PerformanceService

性能监控服务，用于测量和分析应用性能。

```typescript
import { performanceService } from '@/services/PerformanceService'

// 开始性能测量
performanceService.startMeasure('operation-name')

// 结束性能测量
const metric = performanceService.endMeasure('operation-name')

// 异步操作性能测量
const result = await performanceService.measureAsync(
  'async-operation',
  async () => {
    return await someAsyncOperation()
  }
)

// 获取性能报告
const report = performanceService.getPerformanceReport()
```

### CacheService

缓存服务，提供高效的数据缓存和检索功能。

```typescript
import { cacheService } from '@/services/CacheService'

// 设置缓存
cacheService.set('key', data, 60000) // 60秒TTL

// 获取缓存
const cachedData = cacheService.get('key')

// 获取或设置缓存
const data = await cacheService.getOrSet('key', async () => {
  return await fetchData()
}, 60000)

// 批量操作
const results = cacheService.getMultiple(['key1', 'key2', 'key3'])
cacheService.setMultiple(new Map([['key1', data1], ['key2', data2]]))

// 获取缓存统计
const stats = cacheService.getStats()
```

### UndoRedoService

撤销/重做服务，支持命令模式的操作历史管理。

```typescript
import { undoRedoService, FunctionCommand } from '@/services/UndoRedoService'

// 创建并执行命令
const command = new FunctionCommand(
  '添加节点',
  () => {
    // 执行操作
    addNode()
  },
  () => {
    // 撤销操作
    removeNode()
  }
)

await undoRedoService.executeCommand(command)

// 撤销和重做
await undoRedoService.undo()
await undoRedoService.redo()

// 检查状态
const canUndo = undoRedoService.canUndo()
const canRedo = undoRedoService.canRedo()

// 获取历史记录
const history = undoRedoService.getHistory()
```

### KeyboardService

键盘快捷键服务，提供全局快捷键管理。

```typescript
import { keyboardService, registerShortcut } from '@/services/KeyboardService'

// 注册快捷键
const shortcutId = registerShortcut(
  ['ctrl', 's'],
  '保存',
  (event) => {
    event.preventDefault()
    save()
  },
  'editor' // 上下文
)

// 设置上下文
keyboardService.setContext('editor')

// 批量注册快捷键
const shortcutIds = registerShortcuts([
  {
    keys: ['ctrl', 'z'],
    description: '撤销',
    handler: () => undo(),
    context: 'editor'
  },
  {
    keys: ['ctrl', 'y'],
    description: '重做',
    handler: () => redo(),
    context: 'editor'
  }
])

// 注销快捷键
keyboardService.unregister(shortcutId)
```

## 编译器 API

### GraphCompiler

图形编译器，将节点图数据编译为配置文件。

```typescript
import { compileGraph, compileMultipleGraphs } from '@/compiler/graph-compiler'

// 编译单个图形
const config = compileGraph(graphData)

// 编译多个场景
const multiConfig = compileMultipleGraphs(
  {
    'scene1': graphData1,
    'scene2': graphData2
  },
  'scene1' // 默认场景
)
```

## 组件 API

### GraphCanvas

图形画布组件，提供节点图编辑功能。

```vue
<template>
  <GraphCanvas 
    ref="graphCanvasRef"
    @node-selected="handleNodeSelected"
    @graph-changed="handleGraphChanged"
  />
</template>

<script setup>
import GraphCanvas from '@/components/editor/GraphCanvas.vue'

const graphCanvasRef = ref()

// 保存图形数据
const graphData = graphCanvasRef.value?.saveGraphData()

// 加载图形数据
graphCanvasRef.value?.loadGraphData(data)

// 清空图形
graphCanvasRef.value?.clearGraph()
</script>
```

### EditorToolbar

编辑器工具栏组件，提供常用编辑操作。

```vue
<template>
  <EditorToolbar 
    :graph="currentGraph" 
    @compile="handleCompile"
    @save="handleSave"
  />
</template>
```

### SearchFilter

搜索过滤组件，提供数据搜索和过滤功能。

```vue
<template>
  <SearchFilter
    :items="allNodes"
    :filters="filterOptions"
    placeholder="搜索节点..."
    :search-fields="['title', 'description', 'category']"
    @select="handleNodeSelect"
    @action="handleSearchAction"
  />
</template>
```

## 类型定义

### 核心类型

```typescript
// 节点类型
export type NodeType =
  | 'event'
  | 'action-highlight'
  | 'action-callback'
  | 'logic-branch'
  | 'scene-config'
  | 'data-source'
  | 'data-transform'
  | 'data-mapping'
  | 'data-consumer'
  | 'lifecycle'

// 节点数据接口
export interface NodeData {
  nodeType: NodeType
  displayName?: string
  description?: string
  [key: string]: any
}

// 场景接口
export interface Scene {
  id: string
  name: string
  graphData: any
  lastModified: number
  configId?: string
  models?: string[]
  environment?: string
  scene?: string
}

// 应用状态接口
export interface AppState {
  selectedNode: Cell.Properties | null
  currentSceneId: string | null
  generatedConfig: string
  isLibraryCollapsed: boolean
  isPropertiesCollapsed: boolean
}
```

## 事件系统

### 全局事件

DDD-Flow 使用自定义事件进行组件间通信：

```typescript
// 添加节点请求
document.dispatchEvent(new CustomEvent('add-node-request', {
  detail: { type: 'event', subtype: 'click' }
}))

// 更新节点数据
document.dispatchEvent(new CustomEvent('update-node-data', {
  detail: { nodeId: 'node-1', data: newData }
}))

// 场景创建
document.dispatchEvent(new CustomEvent('scene-created', {
  detail: { sceneId: 'scene-1' }
}))
```

### 组件事件

```typescript
// 节点选择事件
interface NodeSelectedEvent {
  node: Cell.Properties | null
}

// 配置生成事件
interface ConfigGeneratedEvent {
  config: CompiledConfig
}

// 节点类型变更事件
interface NodeTypeChangedEvent {
  nodeId: string
  newType: NodeType
  oldType: NodeType
}
```

## 最佳实践

### 错误处理

```typescript
// 使用错误服务处理异步操作
const result = await errorService.withErrorHandling(async () => {
  return await riskyOperation()
}, 'Operation Context')

// 在组件中处理错误
try {
  await someOperation()
} catch (error) {
  errorService.handleError(error as Error, 'Component.method')
}
```

### 性能优化

```typescript
// 使用性能装饰器
@measurePerformance('expensive-operation')
function expensiveOperation() {
  // 耗时操作
}

// 手动性能测量
performanceService.startMeasure('custom-operation')
// 执行操作
performanceService.endMeasure('custom-operation')
```

### 缓存使用

```typescript
// 使用缓存装饰器
@cached(60000) // 60秒缓存
async function fetchData(id: string) {
  return await api.getData(id)
}

// 手动缓存管理
const data = await cacheService.getOrSet(`data-${id}`, async () => {
  return await api.getData(id)
}, 60000)
```
