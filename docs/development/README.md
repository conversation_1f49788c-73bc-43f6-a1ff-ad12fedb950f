# DDD-Flow 开发指南

## 项目结构

```
src/
├── components/           # Vue 组件
│   ├── common/          # 通用组件
│   ├── editor/          # 编辑器相关组件
│   └── scene/           # 场景管理组件
├── services/            # 业务服务层
├── compiler/            # 配置编译器
├── types/               # TypeScript 类型定义
├── utils/               # 工具函数
└── assets/              # 静态资源

docs/
├── api/                 # API 文档
├── development/         # 开发文档
└── user/                # 用户文档

tests/
├── unit/                # 单元测试
├── integration/         # 集成测试
└── e2e/                 # 端到端测试
```

## 开发环境设置

### 前置要求

- Node.js >= 18.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0
- Git

### 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd ddd-flow

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 开发脚本

```bash
# 开发模式
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview

# 运行测试
npm run test

# 运行测试并显示 UI
npm run test:ui

# 运行测试覆盖率
npm run test:coverage

# 代码检查
npm run lint

# 代码格式化
npm run format

# 类型检查
npm run type-check

# CI 检查（包含所有检查）
npm run ci
```

## 架构设计

### 分层架构

DDD-Flow 采用分层架构设计：

1. **表现层（Presentation Layer）**
   - Vue 组件
   - 用户界面交互
   - 事件处理

2. **应用层（Application Layer）**
   - 应用服务
   - 用例协调
   - 事件编排

3. **领域层（Domain Layer）**
   - 业务逻辑
   - 领域模型
   - 领域服务

4. **基础设施层（Infrastructure Layer）**
   - 数据持久化
   - 外部服务集成
   - 技术实现

### 服务设计模式

#### 单例模式

所有核心服务都采用单例模式：

```typescript
export class ServiceName {
  private static instance: ServiceName

  private constructor() {
    // 初始化逻辑
  }

  static getInstance(): ServiceName {
    if (!ServiceName.instance) {
      ServiceName.instance = new ServiceName()
    }
    return ServiceName.instance
  }
}
```

#### 观察者模式

错误处理服务使用观察者模式：

```typescript
class ErrorService {
  private errorHandlers: ErrorHandler[] = []

  addErrorHandler(handler: ErrorHandler): void {
    this.errorHandlers.push(handler)
  }

  private notifyHandlers(error: Error): void {
    this.errorHandlers.forEach(handler => handler(error))
  }
}
```

#### 命令模式

撤销/重做功能使用命令模式：

```typescript
interface Command {
  execute(): void | Promise<void>
  undo(): void | Promise<void>
}

class UndoRedoService {
  private undoStack: Command[] = []
  private redoStack: Command[] = []

  async executeCommand(command: Command): Promise<void> {
    await command.execute()
    this.undoStack.push(command)
    this.redoStack = [] // 清空重做栈
  }
}
```

## 编码规范

### TypeScript 规范

1. **类型定义**
   ```typescript
   // 使用接口定义对象类型
   interface User {
     id: string
     name: string
     email?: string
   }

   // 使用联合类型定义枚举
   type Status = 'pending' | 'success' | 'error'

   // 使用泛型提高复用性
   interface ApiResponse<T> {
     data: T
     status: number
     message: string
   }
   ```

2. **函数定义**
   ```typescript
   // 明确返回类型
   function calculateTotal(items: Item[]): number {
     return items.reduce((sum, item) => sum + item.price, 0)
   }

   // 异步函数
   async function fetchUser(id: string): Promise<User> {
     const response = await api.get(`/users/${id}`)
     return response.data
   }
   ```

3. **错误处理**
   ```typescript
   // 使用 Result 模式
   type Result<T, E = Error> = 
     | { success: true; data: T }
     | { success: false; error: E }

   async function safeOperation(): Promise<Result<Data>> {
     try {
       const data = await riskyOperation()
       return { success: true, data }
     } catch (error) {
       return { success: false, error: error as Error }
     }
   }
   ```

### Vue 组件规范

1. **组件结构**
   ```vue
   <template>
     <!-- 模板内容 -->
   </template>

   <script setup lang="ts">
   // 导入
   import { ref, computed, onMounted } from 'vue'
   import type { ComponentProps } from './types'

   // 接口定义
   interface Props {
     title: string
     items?: Item[]
   }

   // Props 和 Emits
   const props = withDefaults(defineProps<Props>(), {
     items: () => []
   })

   const emit = defineEmits<{
     select: [item: Item]
     change: [value: string]
   }>()

   // 响应式数据
   const isLoading = ref(false)
   const selectedItem = ref<Item | null>(null)

   // 计算属性
   const filteredItems = computed(() => {
     return props.items.filter(item => item.active)
   })

   // 方法
   function handleSelect(item: Item) {
     selectedItem.value = item
     emit('select', item)
   }

   // 生命周期
   onMounted(() => {
     // 初始化逻辑
   })
   </script>

   <style scoped>
   /* 样式 */
   </style>
   ```

2. **组件命名**
   - 使用 PascalCase
   - 多单词组件名
   - 描述性命名

3. **Props 验证**
   ```typescript
   interface Props {
     // 必需属性
     id: string
     
     // 可选属性
     title?: string
     
     // 带默认值的属性
     size?: 'small' | 'medium' | 'large'
     
     // 复杂类型
     items?: Array<{
       id: string
       name: string
     }>
   }

   const props = withDefaults(defineProps<Props>(), {
     title: '',
     size: 'medium',
     items: () => []
   })
   ```

### CSS 规范

1. **BEM 命名法**
   ```css
   .component-name {
     /* 块 */
   }

   .component-name__element {
     /* 元素 */
   }

   .component-name--modifier {
     /* 修饰符 */
   }

   .component-name__element--modifier {
     /* 元素修饰符 */
   }
   ```

2. **CSS 变量**
   ```css
   :root {
     --primary-color: #1890ff;
     --secondary-color: #52c41a;
     --text-color: #262626;
     --background-color: #ffffff;
   }

   .component {
     color: var(--text-color);
     background-color: var(--background-color);
   }
   ```

## 测试策略

### 单元测试

```typescript
import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import Component from '@/components/Component.vue'

describe('Component', () => {
  it('should render correctly', () => {
    const wrapper = mount(Component, {
      props: {
        title: 'Test Title'
      }
    })

    expect(wrapper.text()).toContain('Test Title')
  })

  it('should emit event when clicked', async () => {
    const wrapper = mount(Component)
    
    await wrapper.find('button').trigger('click')
    
    expect(wrapper.emitted('click')).toBeTruthy()
  })
})
```

### 集成测试

```typescript
import { describe, it, expect } from 'vitest'
import { sceneService } from '@/services/SceneService'

describe('SceneService Integration', () => {
  it('should create and retrieve scene', () => {
    const scene = sceneService.createScene('Test Scene')
    
    expect(scene.id).toBeDefined()
    expect(scene.name).toBe('Test Scene')
    
    const retrieved = sceneService.getScene(scene.id)
    expect(retrieved).toEqual(scene)
  })
})
```

### E2E 测试

```typescript
import { test, expect } from '@playwright/test'

test('should create new scene', async ({ page }) => {
  await page.goto('/')
  
  await page.click('[data-testid="create-scene-btn"]')
  await page.fill('[data-testid="scene-name-input"]', 'New Scene')
  await page.click('[data-testid="confirm-btn"]')
  
  await expect(page.locator('[data-testid="scene-list"]')).toContainText('New Scene')
})
```

## 性能优化

### 代码分割

```typescript
// 路由级别的代码分割
const routes = [
  {
    path: '/editor',
    component: () => import('@/views/Editor.vue')
  }
]

// 组件级别的代码分割
const HeavyComponent = defineAsyncComponent(() => import('@/components/HeavyComponent.vue'))
```

### 缓存策略

```typescript
// 使用缓存装饰器
@cached(300000) // 5分钟缓存
async function fetchExpensiveData(id: string) {
  return await api.getData(id)
}

// 手动缓存管理
const cachedData = await cacheService.getOrSet(
  `data-${id}`,
  () => fetchData(id),
  300000
)
```

### 性能监控

```typescript
// 使用性能装饰器
@measurePerformance('component-render')
function renderComponent() {
  // 渲染逻辑
}

// 手动性能测量
performanceService.startMeasure('operation')
// 执行操作
const metric = performanceService.endMeasure('operation')
```

## 部署指南

### 构建配置

```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vue: ['vue'],
          x6: ['@antv/x6'],
          services: ['./src/services/*']
        }
      }
    }
  }
})
```

### 环境变量

```bash
# .env.development
VITE_API_BASE_URL=http://localhost:3000/api
VITE_DEBUG=true

# .env.production
VITE_API_BASE_URL=https://api.example.com
VITE_DEBUG=false
```

## 贡献指南

### 提交规范

使用 Conventional Commits 规范：

```
feat: 添加新功能
fix: 修复 bug
docs: 更新文档
style: 代码格式调整
refactor: 重构代码
test: 添加测试
chore: 构建工具或辅助工具的变动
```

### Pull Request 流程

1. Fork 项目
2. 创建功能分支
3. 提交变更
4. 创建 Pull Request
5. 代码审查
6. 合并到主分支

### 代码审查清单

- [ ] 代码符合项目规范
- [ ] 包含适当的测试
- [ ] 文档已更新
- [ ] 性能影响已评估
- [ ] 安全性已考虑
