# DDD-Flow 用户指南

## 概述

DDD-Flow 是一个基于领域驱动设计（DDD）的可视化场景编辑器，帮助用户通过拖拽节点的方式创建复杂的交互场景配置。

## 快速开始

### 启动应用

1. 打开浏览器访问应用地址
2. 应用将自动加载默认场景
3. 开始创建你的第一个交互场景

### 界面介绍

应用界面分为四个主要区域：

1. **顶部工具栏**：包含场景导入、面板切换等全局操作
2. **左侧面板**：场景管理器和节点库
3. **中央编辑区**：图形编辑画布和配置输出
4. **右侧面板**：属性编辑器和小地图

## 场景管理

### 创建场景

1. 在左侧场景管理器中点击"创建场景"按钮
2. 输入场景名称和配置ID
3. 点击确认创建新场景

### 切换场景

1. 在场景列表中点击要切换的场景
2. 系统会自动保存当前场景并加载新场景

### 删除场景

1. 在场景列表中找到要删除的场景
2. 点击删除按钮并确认操作

### 重命名场景

1. 双击场景名称进入编辑模式
2. 输入新名称并按回车确认

## 节点编辑

### 添加节点

#### 方法一：从节点库拖拽
1. 在左侧节点库中找到需要的节点类型
2. 点击节点添加到画布中央

#### 方法二：右键菜单
1. 在画布空白处右键
2. 从上下文菜单中选择要添加的节点类型

#### 方法三：搜索添加
1. 点击节点库顶部的搜索按钮
2. 在搜索框中输入节点名称或描述
3. 从搜索结果中选择节点

### 节点类型

#### 场景配置节点
- **用途**：定义场景的基本信息
- **属性**：场景名称、模型文件、环境设置等
- **连接**：作为场景的根节点，不接受输入连接

#### 事件节点
- **双击事件**：响应鼠标双击操作
- **单击事件**：响应鼠标单击操作
- **悬停事件**：响应鼠标悬停操作
- **右键双击事件**：响应鼠标右键双击操作

#### 动作节点
- **高亮动作**：高亮显示指定的网格对象
- **相机聚焦**：将相机聚焦到指定对象
- **相机移动**：移动相机到指定位置
- **播放动画**：播放网格对象的动画
- **显示消息**：在界面上显示提示消息

#### 逻辑节点
- **条件分支**：根据条件执行不同的分支
- **顺序执行**：按顺序执行多个操作

#### 数据节点
- **轮询数据源**：定时轮询获取数据
- **WebSocket数据源**：通过WebSocket实时获取数据
- **映射转换**：将数据值映射为其他值
- **范围转换**：将数据值转换到指定范围
- **CSV映射文件**：使用CSV文件进行数据映射
- **切换可见性**：根据数据切换对象可见性

#### 生命周期节点
- **场景激活**：场景激活时触发
- **场景退出**：场景退出时触发
- **模型加载完成**：模型加载完成时触发
- **场景初始化**：场景初始化时触发

### 编辑节点

#### 选择节点
1. 点击画布中的节点进行选择
2. 选中的节点会显示选择框
3. 右侧属性面板会显示节点的详细属性

#### 移动节点
1. 选中节点后拖拽到新位置
2. 使用键盘方向键进行精确移动

#### 删除节点
1. 选中要删除的节点
2. 按 Delete 键或 Backspace 键
3. 或者右键选择删除选项

#### 复制粘贴节点
1. 选中节点后按 Ctrl+C 复制
2. 按 Ctrl+V 粘贴节点
3. 粘贴的节点会出现在原节点旁边

### 连接节点

#### 创建连接
1. 将鼠标悬停在节点的输出端口上
2. 拖拽到目标节点的输入端口
3. 释放鼠标完成连接

#### 删除连接
1. 选中要删除的连接线
2. 按 Delete 键删除
3. 或者右键选择删除选项

## 属性编辑

### 基本属性

每个节点都有以下基本属性：
- **显示名称**：节点在画布上显示的名称
- **描述**：节点的详细描述信息
- **节点类型**：节点的类型（只读）

### 特定属性

不同类型的节点有不同的特定属性：

#### 事件节点属性
- **网格名称**：触发事件的网格对象名称列表
- **事件类型**：事件的具体类型

#### 高亮动作属性
- **颜色**：高亮的颜色值（RGB）
- **持续时间**：高亮持续的时间（毫秒）
- **强度**：高亮的强度值

#### 相机动作属性
- **目标对象**：相机聚焦的目标对象
- **持续时间**：动画持续时间
- **缓动函数**：动画的缓动效果

#### 数据源属性
- **数据URL**：数据源的URL地址
- **轮询间隔**：轮询的时间间隔
- **数据格式**：数据的格式类型

### 动态属性编辑

某些属性支持动态值：
- 使用 `{{变量名}}` 语法引用其他节点的数据
- 支持简单的表达式计算
- 可以引用全局变量和上下文数据

## 快捷键

### 通用快捷键
- **Ctrl+S**：保存当前场景
- **Ctrl+Z**：撤销操作
- **Ctrl+Y**：重做操作
- **Ctrl+A**：全选节点
- **Delete/Backspace**：删除选中的节点或连接

### 编辑快捷键
- **Ctrl+C**：复制选中的节点
- **Ctrl+V**：粘贴节点
- **Ctrl+X**：剪切选中的节点
- **Esc**：取消当前操作

### 视图快捷键
- **Ctrl+0**：适应画布大小
- **Ctrl++**：放大视图
- **Ctrl+-**：缩小视图

## 配置生成

### 生成配置

1. 完成场景编辑后，点击"生成配置"按钮
2. 系统会编译当前场景的节点图
3. 生成的配置会显示在编辑区下方

### 配置格式

生成的配置采用JSON格式，包含以下主要部分：
- **场景信息**：场景的基本配置
- **事件定义**：所有事件节点的配置
- **动作定义**：所有动作节点的配置
- **数据流**：节点之间的连接关系

### 导出配置

1. 生成配置后，可以复制配置内容
2. 将配置保存为JSON文件
3. 在目标系统中使用该配置文件

## 高级功能

### 搜索和过滤

#### 节点搜索
1. 点击节点库顶部的搜索图标
2. 输入搜索关键词
3. 系统会实时过滤匹配的节点

#### 过滤器
1. 在搜索界面中点击"显示过滤器"
2. 选择要显示的节点类型
3. 可以组合多个过滤条件

### 批量操作

#### 多选节点
1. 按住 Ctrl 键点击多个节点
2. 或者拖拽选择框选中多个节点
3. 可以对选中的节点进行批量操作

#### 批量删除
1. 选中多个节点
2. 按 Delete 键删除所有选中的节点

#### 批量移动
1. 选中多个节点
2. 拖拽其中一个节点，所有选中的节点会一起移动

### 场景导入

#### 从配置文件导入
1. 点击顶部工具栏的"从config.js导入场景"按钮
2. 系统会解析现有的配置文件
3. 自动创建对应的场景和节点图

## 故障排除

### 常见问题

#### 节点无法连接
- 检查节点的端口类型是否匹配
- 确保连接方向正确（输出到输入）
- 某些节点类型可能不支持特定的连接

#### 配置生成失败
- 检查是否存在未连接的节点
- 确保所有必需的属性都已设置
- 检查节点图是否存在循环依赖

#### 场景切换异常
- 确保场景数据已正确保存
- 检查浏览器控制台是否有错误信息
- 尝试刷新页面重新加载

### 性能优化

#### 大型场景优化
- 避免创建过多的节点（建议少于100个）
- 合理使用节点分组和层次结构
- 定期清理不需要的节点和连接

#### 内存管理
- 定期保存场景数据
- 避免长时间运行而不刷新页面
- 关闭不需要的浏览器标签页

## 技术支持

如果遇到问题或需要帮助，请：

1. 查看浏览器控制台的错误信息
2. 检查网络连接状态
3. 尝试刷新页面或重启浏览器
4. 联系技术支持团队

## 更新日志

### 版本 1.0.0
- 初始版本发布
- 支持基本的节点编辑功能
- 实现场景管理和配置生成

### 版本 1.1.0
- 添加搜索和过滤功能
- 实现撤销/重做功能
- 优化性能和用户体验

### 版本 1.2.0
- 添加键盘快捷键支持
- 实现批量操作功能
- 增强错误处理和提示
