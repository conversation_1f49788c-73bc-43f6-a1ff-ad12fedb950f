{"version": 3, "sources": ["../../../../../node_modules/.pnpm/@antv+x6-plugin-minimap@2.0.7_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-minimap/src/style/raw.ts", "../../../../../node_modules/.pnpm/@antv+x6-plugin-minimap@2.0.7_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-minimap/src/index.ts"], "sourcesContent": ["/* eslint-disable */\n\n/**\n * Auto generated file, do not modify it!\n */\n\nexport const content = `.x6-widget-minimap {\n  position: relative;\n  display: table-cell;\n  box-sizing: border-box;\n  overflow: hidden;\n  text-align: center;\n  vertical-align: middle;\n  background-color: #fff;\n  user-select: none;\n}\n.x6-widget-minimap .x6-graph {\n  display: inline-block;\n  box-shadow: 0 0 4px 0 #eee;\n  cursor: pointer;\n}\n.x6-widget-minimap .x6-graph > svg {\n  pointer-events: none;\n  shape-rendering: optimizespeed;\n}\n.x6-widget-minimap .x6-graph .x6-node * {\n  /* stylelint-disable-next-line */\n  vector-effect: initial;\n}\n.x6-widget-minimap-viewport {\n  position: absolute;\n  box-sizing: content-box !important;\n  margin: -2px 0 0 -2px;\n  border: 2px solid #31d0c6;\n  cursor: move;\n}\n.x6-widget-minimap-viewport-zoom {\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  box-sizing: border-box;\n  width: 12px;\n  height: 12px;\n  margin: 0 -6px -6px 0;\n  background-color: #fff;\n  border: 2px solid #31d0c6;\n  border-radius: 50%;\n  cursor: nwse-resize;\n}\n`\n", "import { Function<PERSON>x<PERSON>, <PERSON>ss<PERSON>oa<PERSON>, <PERSON>, View, Graph, EventArgs } from '@antv/x6'\nimport { content } from './style/raw'\n\nexport class MiniMap extends View implements Graph.Plugin {\n  public name = 'minimap'\n  private graph: Graph\n  public readonly options: MiniMap.Options\n  public container: HTMLDivElement\n  protected zoomHandle: HTMLDivElement\n  protected viewport: HTMLElement\n  protected sourceGraph: Graph\n  protected targetGraph: Graph\n  protected geometry: Util.ViewGeometry\n  protected ratio: number\n  // Marks whether targetGraph is being transformed or scaled\n  // If yes we update updateViewport only\n  private targetGraphTransforming: boolean\n\n  protected get scroller() {\n    return this.graph.getPlugin<any>('scroller')\n  }\n\n  protected get graphContainer() {\n    if (this.scroller) {\n      return this.scroller.container\n    }\n    return this.graph.container\n  }\n\n  constructor(options: Partial<MiniMap.Options>) {\n    super()\n\n    this.options = {\n      ...Util.defaultOptions,\n      ...options,\n    } as MiniMap.Options\n\n    CssLoader.ensure(this.name, content)\n  }\n\n  public init(graph: Graph) {\n    this.graph = graph\n\n    this.updateViewport = FunctionExt.debounce(\n      this.updateViewport.bind(this),\n      0,\n    )\n\n    this.container = document.createElement('div')\n    Dom.addClass(this.container, this.prefixClassName(ClassName.root))\n\n    const graphContainer = document.createElement('div')\n    this.container.appendChild(graphContainer)\n\n    this.viewport = document.createElement('div')\n    Dom.addClass(this.viewport, this.prefixClassName(ClassName.viewport))\n\n    if (this.options.scalable) {\n      this.zoomHandle = document.createElement('div')\n      Dom.addClass(this.zoomHandle, this.prefixClassName(ClassName.zoom))\n      Dom.appendTo(this.zoomHandle, this.viewport)\n    }\n\n    Dom.append(this.container, this.viewport)\n    Dom.css(this.container, {\n      width: this.options.width,\n      height: this.options.height,\n      padding: this.options.padding,\n    })\n\n    if (this.options.container) {\n      this.options.container.appendChild(this.container)\n    }\n\n    this.sourceGraph = this.graph\n    const targetGraphOptions: Graph.Options = {\n      ...this.options.graphOptions,\n      container: graphContainer,\n      model: this.sourceGraph.model,\n      interacting: false,\n      grid: false,\n      background: false,\n      embedding: false,\n    }\n\n    this.targetGraph = this.options.createGraph\n      ? this.options.createGraph(targetGraphOptions)\n      : new Graph(targetGraphOptions)\n\n    this.updatePaper(\n      this.sourceGraph.options.width,\n      this.sourceGraph.options.height,\n    )\n\n    this.startListening()\n  }\n\n  protected startListening() {\n    if (this.scroller) {\n      Dom.Event.on(\n        this.graphContainer,\n        `scroll${this.getEventNamespace()}`,\n        this.updateViewport,\n      )\n    } else {\n      this.sourceGraph.on('translate', this.onTransform, this)\n      this.sourceGraph.on('scale', this.onTransform, this)\n      this.sourceGraph.on('model:updated', this.onModelUpdated, this)\n    }\n    this.sourceGraph.on('resize', this.updatePaper, this)\n    this.delegateEvents({\n      mousedown: 'startAction',\n      touchstart: 'startAction',\n      [`mousedown .${this.prefixClassName('graph')}`]: 'scrollTo',\n      [`touchstart .${this.prefixClassName('graph')}`]: 'scrollTo',\n    })\n  }\n\n  protected stopListening() {\n    if (this.scroller) {\n      Dom.Event.off(this.graphContainer, this.getEventNamespace())\n    } else {\n      this.sourceGraph.off('translate', this.onTransform, this)\n      this.sourceGraph.off('scale', this.onTransform, this)\n      this.sourceGraph.off('model:updated', this.onModelUpdated, this)\n    }\n    this.sourceGraph.off('resize', this.updatePaper, this)\n    this.undelegateEvents()\n  }\n\n  protected onRemove() {\n    this.stopListening()\n    this.targetGraph.dispose(false)\n  }\n\n  protected onTransform(options: { ui: boolean }) {\n    if (options.ui || this.targetGraphTransforming || !this.scroller) {\n      this.updateViewport()\n    }\n  }\n\n  protected onModelUpdated() {\n    this.targetGraph.zoomToFit()\n  }\n\n  protected updatePaper(width: number, height: number): this\n  protected updatePaper({ width, height }: EventArgs['resize']): this\n  protected updatePaper(w: number | EventArgs['resize'], h?: number) {\n    let width: number\n    let height: number\n    if (typeof w === 'object') {\n      width = w.width\n      height = w.height\n    } else {\n      width = w\n      height = h as number\n    }\n\n    const origin = this.sourceGraph.options\n    const scale = this.sourceGraph.transform.getScale()\n    const maxWidth = this.options.width - 2 * this.options.padding\n    const maxHeight = this.options.height - 2 * this.options.padding\n\n    width /= scale.sx // eslint-disable-line\n    height /= scale.sy // eslint-disable-line\n\n    this.ratio = Math.min(maxWidth / width, maxHeight / height)\n\n    const ratio = this.ratio\n    const x = (origin.x * ratio) / scale.sx\n    const y = (origin.y * ratio) / scale.sy\n\n    width *= ratio // eslint-disable-line\n    height *= ratio // eslint-disable-line\n    this.targetGraph.resize(width, height)\n    this.targetGraph.translate(x, y)\n\n    if (this.scroller) {\n      this.targetGraph.scale(ratio, ratio)\n    } else {\n      this.targetGraph.zoomToFit()\n    }\n\n    this.updateViewport()\n    return this\n  }\n\n  protected updateViewport() {\n    const sourceGraphScale = this.sourceGraph.transform.getScale()\n    const targetGraphScale = this.targetGraph.transform.getScale()\n\n    let origin = null\n    if (this.scroller) {\n      origin = this.scroller.clientToLocalPoint(0, 0)\n    } else {\n      origin = this.graph.graphToLocal(0, 0)\n    }\n\n    const position = Dom.position(this.targetGraph.container)\n    const translation = this.targetGraph.translate()\n    translation.ty = translation.ty || 0\n\n    this.geometry = {\n      top: position.top + origin.y * targetGraphScale.sy + translation.ty,\n      left: position.left + origin.x * targetGraphScale.sx + translation.tx,\n      width:\n        (this.graphContainer.clientWidth! * targetGraphScale.sx) /\n        sourceGraphScale.sx,\n      height:\n        (this.graphContainer.clientHeight! * targetGraphScale.sy) /\n        sourceGraphScale.sy,\n    }\n    Dom.css(this.viewport, this.geometry)\n  }\n\n  protected startAction(evt: Dom.MouseDownEvent) {\n    const e = this.normalizeEvent(evt)\n    const action = e.target === this.zoomHandle ? 'zooming' : 'panning'\n    const { tx, ty } = this.sourceGraph.translate()\n    const eventData: Util.EventData = {\n      action,\n      clientX: e.clientX,\n      clientY: e.clientY,\n      scrollLeft: this.graphContainer.scrollLeft,\n      scrollTop: this.graphContainer.scrollTop,\n      zoom: this.sourceGraph.zoom(),\n      scale: this.sourceGraph.transform.getScale(),\n      geometry: this.geometry,\n      translateX: tx,\n      translateY: ty,\n    }\n    this.targetGraphTransforming = true\n    this.delegateDocumentEvents(Util.documentEvents, eventData)\n  }\n\n  protected doAction(evt: Dom.MouseMoveEvent) {\n    const e = this.normalizeEvent(evt)\n    const clientX = e.clientX\n    const clientY = e.clientY\n    const data = e.data as Util.EventData\n    switch (data.action) {\n      case 'panning': {\n        const scale = this.sourceGraph.transform.getScale()\n        const rx = (clientX - data.clientX) * scale.sx\n        const ry = (clientY - data.clientY) * scale.sy\n        if (this.scroller) {\n          this.graphContainer.scrollLeft = data.scrollLeft + rx / this.ratio\n          this.graphContainer.scrollTop = data.scrollTop + ry / this.ratio\n        } else {\n          this.sourceGraph.translate(\n            data.translateX - rx / this.ratio,\n            data.translateY - ry / this.ratio,\n          )\n        }\n        break\n      }\n\n      case 'zooming': {\n        const startScale = data.scale\n        const startGeometry = data.geometry\n        const delta =\n          1 + (data.clientX - clientX) / startGeometry.width / startScale.sx\n\n        if (data.frameId) {\n          cancelAnimationFrame(data.frameId)\n        }\n\n        data.frameId = requestAnimationFrame(() => {\n          this.sourceGraph.zoom(delta * data.zoom, {\n            absolute: true,\n            minScale: this.options.minScale,\n            maxScale: this.options.maxScale,\n          })\n        })\n        break\n      }\n\n      default:\n        break\n    }\n  }\n\n  protected stopAction() {\n    this.undelegateDocumentEvents()\n    this.targetGraphTransforming = false\n  }\n\n  protected scrollTo(evt: Dom.MouseDownEvent) {\n    const e = this.normalizeEvent(evt)\n\n    let x\n    let y\n\n    const ts = this.targetGraph.translate()\n    ts.ty = ts.ty || 0\n\n    if (e.offsetX == null) {\n      const offset = Dom.offset(this.targetGraph.container)\n      x = e.pageX - offset.left\n      y = e.pageY - offset.top\n    } else {\n      x = e.offsetX\n      y = e.offsetY\n    }\n\n    const cx = (x - ts.tx) / this.ratio\n    const cy = (y - ts.ty) / this.ratio\n    this.sourceGraph.centerPoint(cx, cy)\n  }\n\n  @View.dispose()\n  dispose() {\n    this.remove()\n    CssLoader.clean(this.name)\n  }\n}\n\nnamespace ClassName {\n  export const root = 'widget-minimap'\n  export const viewport = `${root}-viewport`\n  export const zoom = `${viewport}-zoom`\n}\nexport namespace MiniMap {\n  export interface Options {\n    container: HTMLElement\n    width: number\n    height: number\n    padding: number\n    scalable?: boolean\n    minScale?: number\n    maxScale?: number\n    createGraph?: (options: Graph.Options) => Graph\n    graphOptions?: Graph.Options\n  }\n}\n\nnamespace Util {\n  export const defaultOptions: Partial<MiniMap.Options> = {\n    width: 300,\n    height: 200,\n    padding: 10,\n    scalable: true,\n    minScale: 0.01,\n    maxScale: 16,\n    graphOptions: {},\n    createGraph: (options) => new Graph(options),\n  }\n\n  export const documentEvents = {\n    mousemove: 'doAction',\n    touchmove: 'doAction',\n    mouseup: 'stopAction',\n    touchend: 'stopAction',\n  }\n\n  export interface ViewGeometry extends Record<string, number> {\n    top: number\n    left: number\n    width: number\n    height: number\n  }\n\n  export interface EventData {\n    frameId?: number\n    action: 'zooming' | 'panning'\n    clientX: number\n    clientY: number\n    scrollLeft: number\n    scrollTop: number\n    zoom: number\n    scale: { sx: number; sy: number }\n    geometry: ViewGeometry\n    translateX: number\n    translateY: number\n  }\n}\n"], "mappings": ";;;;;;;;;;AAMO,IAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACHjB,IAAO,UAAP,cAAuB,KAAI;EAe/B,IAAc,WAAQ;AACpB,WAAO,KAAK,MAAM,UAAe,UAAU;EAC7C;EAEA,IAAc,iBAAc;AAC1B,QAAI,KAAK,UAAU;AACjB,aAAO,KAAK,SAAS;;AAEvB,WAAO,KAAK,MAAM;EACpB;EAEA,YAAY,SAAiC;AAC3C,UAAK;AA1BA,SAAA,OAAO;AA4BZ,SAAK,UAAU,OAAA,OAAA,OAAA,OAAA,CAAA,GACV,KAAK,cAAc,GACnB,OAAO;AAGZ,mBAAU,OAAO,KAAK,MAAM,OAAO;EACrC;EAEO,KAAK,OAAY;AACtB,SAAK,QAAQ;AAEb,SAAK,iBAAiB,aAAY,SAChC,KAAK,eAAe,KAAK,IAAI,GAC7B,CAAC;AAGH,SAAK,YAAY,SAAS,cAAc,KAAK;AAC7C,IAAAA,cAAI,SAAS,KAAK,WAAW,KAAK,gBAAgB,UAAU,IAAI,CAAC;AAEjE,UAAM,iBAAiB,SAAS,cAAc,KAAK;AACnD,SAAK,UAAU,YAAY,cAAc;AAEzC,SAAK,WAAW,SAAS,cAAc,KAAK;AAC5C,IAAAA,cAAI,SAAS,KAAK,UAAU,KAAK,gBAAgB,UAAU,QAAQ,CAAC;AAEpE,QAAI,KAAK,QAAQ,UAAU;AACzB,WAAK,aAAa,SAAS,cAAc,KAAK;AAC9C,MAAAA,cAAI,SAAS,KAAK,YAAY,KAAK,gBAAgB,UAAU,IAAI,CAAC;AAClE,MAAAA,cAAI,SAAS,KAAK,YAAY,KAAK,QAAQ;;AAG7C,IAAAA,cAAI,OAAO,KAAK,WAAW,KAAK,QAAQ;AACxC,IAAAA,cAAI,IAAI,KAAK,WAAW;MACtB,OAAO,KAAK,QAAQ;MACpB,QAAQ,KAAK,QAAQ;MACrB,SAAS,KAAK,QAAQ;KACvB;AAED,QAAI,KAAK,QAAQ,WAAW;AAC1B,WAAK,QAAQ,UAAU,YAAY,KAAK,SAAS;;AAGnD,SAAK,cAAc,KAAK;AACxB,UAAM,qBAAkB,OAAA,OAAA,OAAA,OAAA,CAAA,GACnB,KAAK,QAAQ,YAAY,GAAA,EAC5B,WAAW,gBACX,OAAO,KAAK,YAAY,OACxB,aAAa,OACb,MAAM,OACN,YAAY,OACZ,WAAW,MAAK,CAAA;AAGlB,SAAK,cAAc,KAAK,QAAQ,cAC5B,KAAK,QAAQ,YAAY,kBAAkB,IAC3C,IAAI,MAAM,kBAAkB;AAEhC,SAAK,YACH,KAAK,YAAY,QAAQ,OACzB,KAAK,YAAY,QAAQ,MAAM;AAGjC,SAAK,eAAc;EACrB;EAEU,iBAAc;AACtB,QAAI,KAAK,UAAU;AACjB,MAAAA,cAAI,MAAM,GACR,KAAK,gBACL,SAAS,KAAK,kBAAiB,CAAE,IACjC,KAAK,cAAc;WAEhB;AACL,WAAK,YAAY,GAAG,aAAa,KAAK,aAAa,IAAI;AACvD,WAAK,YAAY,GAAG,SAAS,KAAK,aAAa,IAAI;AACnD,WAAK,YAAY,GAAG,iBAAiB,KAAK,gBAAgB,IAAI;;AAEhE,SAAK,YAAY,GAAG,UAAU,KAAK,aAAa,IAAI;AACpD,SAAK,eAAe;MAClB,WAAW;MACX,YAAY;MACZ,CAAC,cAAc,KAAK,gBAAgB,OAAO,CAAC,EAAE,GAAG;MACjD,CAAC,eAAe,KAAK,gBAAgB,OAAO,CAAC,EAAE,GAAG;KACnD;EACH;EAEU,gBAAa;AACrB,QAAI,KAAK,UAAU;AACjB,MAAAA,cAAI,MAAM,IAAI,KAAK,gBAAgB,KAAK,kBAAiB,CAAE;WACtD;AACL,WAAK,YAAY,IAAI,aAAa,KAAK,aAAa,IAAI;AACxD,WAAK,YAAY,IAAI,SAAS,KAAK,aAAa,IAAI;AACpD,WAAK,YAAY,IAAI,iBAAiB,KAAK,gBAAgB,IAAI;;AAEjE,SAAK,YAAY,IAAI,UAAU,KAAK,aAAa,IAAI;AACrD,SAAK,iBAAgB;EACvB;EAEU,WAAQ;AAChB,SAAK,cAAa;AAClB,SAAK,YAAY,QAAQ,KAAK;EAChC;EAEU,YAAY,SAAwB;AAC5C,QAAI,QAAQ,MAAM,KAAK,2BAA2B,CAAC,KAAK,UAAU;AAChE,WAAK,eAAc;;EAEvB;EAEU,iBAAc;AACtB,SAAK,YAAY,UAAS;EAC5B;EAIU,YAAY,GAAiC,GAAU;AAC/D,QAAI;AACJ,QAAI;AACJ,QAAI,OAAO,MAAM,UAAU;AACzB,cAAQ,EAAE;AACV,eAAS,EAAE;WACN;AACL,cAAQ;AACR,eAAS;;AAGX,UAAM,SAAS,KAAK,YAAY;AAChC,UAAM,QAAQ,KAAK,YAAY,UAAU,SAAQ;AACjD,UAAM,WAAW,KAAK,QAAQ,QAAQ,IAAI,KAAK,QAAQ;AACvD,UAAM,YAAY,KAAK,QAAQ,SAAS,IAAI,KAAK,QAAQ;AAEzD,aAAS,MAAM;AACf,cAAU,MAAM;AAEhB,SAAK,QAAQ,KAAK,IAAI,WAAW,OAAO,YAAY,MAAM;AAE1D,UAAM,QAAQ,KAAK;AACnB,UAAM,IAAK,OAAO,IAAI,QAAS,MAAM;AACrC,UAAM,IAAK,OAAO,IAAI,QAAS,MAAM;AAErC,aAAS;AACT,cAAU;AACV,SAAK,YAAY,OAAO,OAAO,MAAM;AACrC,SAAK,YAAY,UAAU,GAAG,CAAC;AAE/B,QAAI,KAAK,UAAU;AACjB,WAAK,YAAY,MAAM,OAAO,KAAK;WAC9B;AACL,WAAK,YAAY,UAAS;;AAG5B,SAAK,eAAc;AACnB,WAAO;EACT;EAEU,iBAAc;AACtB,UAAM,mBAAmB,KAAK,YAAY,UAAU,SAAQ;AAC5D,UAAM,mBAAmB,KAAK,YAAY,UAAU,SAAQ;AAE5D,QAAI,SAAS;AACb,QAAI,KAAK,UAAU;AACjB,eAAS,KAAK,SAAS,mBAAmB,GAAG,CAAC;WACzC;AACL,eAAS,KAAK,MAAM,aAAa,GAAG,CAAC;;AAGvC,UAAM,WAAWA,cAAI,SAAS,KAAK,YAAY,SAAS;AACxD,UAAM,cAAc,KAAK,YAAY,UAAS;AAC9C,gBAAY,KAAK,YAAY,MAAM;AAEnC,SAAK,WAAW;MACd,KAAK,SAAS,MAAM,OAAO,IAAI,iBAAiB,KAAK,YAAY;MACjE,MAAM,SAAS,OAAO,OAAO,IAAI,iBAAiB,KAAK,YAAY;MACnE,OACG,KAAK,eAAe,cAAe,iBAAiB,KACrD,iBAAiB;MACnB,QACG,KAAK,eAAe,eAAgB,iBAAiB,KACtD,iBAAiB;;AAErB,IAAAA,cAAI,IAAI,KAAK,UAAU,KAAK,QAAQ;EACtC;EAEU,YAAY,KAAuB;AAC3C,UAAM,IAAI,KAAK,eAAe,GAAG;AACjC,UAAM,SAAS,EAAE,WAAW,KAAK,aAAa,YAAY;AAC1D,UAAM,EAAE,IAAI,GAAE,IAAK,KAAK,YAAY,UAAS;AAC7C,UAAM,YAA4B;MAChC;MACA,SAAS,EAAE;MACX,SAAS,EAAE;MACX,YAAY,KAAK,eAAe;MAChC,WAAW,KAAK,eAAe;MAC/B,MAAM,KAAK,YAAY,KAAI;MAC3B,OAAO,KAAK,YAAY,UAAU,SAAQ;MAC1C,UAAU,KAAK;MACf,YAAY;MACZ,YAAY;;AAEd,SAAK,0BAA0B;AAC/B,SAAK,uBAAuB,KAAK,gBAAgB,SAAS;EAC5D;EAEU,SAAS,KAAuB;AACxC,UAAM,IAAI,KAAK,eAAe,GAAG;AACjC,UAAM,UAAU,EAAE;AAClB,UAAM,UAAU,EAAE;AAClB,UAAM,OAAO,EAAE;AACf,YAAQ,KAAK,QAAQ;MACnB,KAAK,WAAW;AACd,cAAM,QAAQ,KAAK,YAAY,UAAU,SAAQ;AACjD,cAAM,MAAM,UAAU,KAAK,WAAW,MAAM;AAC5C,cAAM,MAAM,UAAU,KAAK,WAAW,MAAM;AAC5C,YAAI,KAAK,UAAU;AACjB,eAAK,eAAe,aAAa,KAAK,aAAa,KAAK,KAAK;AAC7D,eAAK,eAAe,YAAY,KAAK,YAAY,KAAK,KAAK;eACtD;AACL,eAAK,YAAY,UACf,KAAK,aAAa,KAAK,KAAK,OAC5B,KAAK,aAAa,KAAK,KAAK,KAAK;;AAGrC;;MAGF,KAAK,WAAW;AACd,cAAM,aAAa,KAAK;AACxB,cAAM,gBAAgB,KAAK;AAC3B,cAAM,QACJ,KAAK,KAAK,UAAU,WAAW,cAAc,QAAQ,WAAW;AAElE,YAAI,KAAK,SAAS;AAChB,+BAAqB,KAAK,OAAO;;AAGnC,aAAK,UAAU,sBAAsB,MAAK;AACxC,eAAK,YAAY,KAAK,QAAQ,KAAK,MAAM;YACvC,UAAU;YACV,UAAU,KAAK,QAAQ;YACvB,UAAU,KAAK,QAAQ;WACxB;QACH,CAAC;AACD;;MAGF;AACE;;EAEN;EAEU,aAAU;AAClB,SAAK,yBAAwB;AAC7B,SAAK,0BAA0B;EACjC;EAEU,SAAS,KAAuB;AACxC,UAAM,IAAI,KAAK,eAAe,GAAG;AAEjC,QAAI;AACJ,QAAI;AAEJ,UAAM,KAAK,KAAK,YAAY,UAAS;AACrC,OAAG,KAAK,GAAG,MAAM;AAEjB,QAAI,EAAE,WAAW,MAAM;AACrB,YAAM,SAASA,cAAI,OAAO,KAAK,YAAY,SAAS;AACpD,UAAI,EAAE,QAAQ,OAAO;AACrB,UAAI,EAAE,QAAQ,OAAO;WAChB;AACL,UAAI,EAAE;AACN,UAAI,EAAE;;AAGR,UAAM,MAAM,IAAI,GAAG,MAAM,KAAK;AAC9B,UAAM,MAAM,IAAI,GAAG,MAAM,KAAK;AAC9B,SAAK,YAAY,YAAY,IAAI,EAAE;EACrC;EAGA,UAAO;AACL,SAAK,OAAM;AACX,mBAAU,MAAM,KAAK,IAAI;EAC3B;;AAHA,WAAA;EADC,KAAK,QAAO;;AAOf,IAAU;CAAV,SAAUC,YAAS;AACJ,EAAAA,WAAA,OAAO;AACP,EAAAA,WAAA,WAAW,GAAGA,WAAA,IAAI;AAClB,EAAAA,WAAA,OAAO,GAAGA,WAAA,QAAQ;AACjC,GAJU,cAAA,YAAS,CAAA,EAAA;AAmBnB,IAAU;CAAV,SAAUC,OAAI;AACC,EAAAA,MAAA,iBAA2C;IACtD,OAAO;IACP,QAAQ;IACR,SAAS;IACT,UAAU;IACV,UAAU;IACV,UAAU;IACV,cAAc,CAAA;IACd,aAAa,CAAC,YAAY,IAAI,MAAM,OAAO;;AAGhC,EAAAA,MAAA,iBAAiB;IAC5B,WAAW;IACX,WAAW;IACX,SAAS;IACT,UAAU;;AAuBd,GAvCU,SAAA,OAAI,CAAA,EAAA;", "names": ["main_exports", "ClassName", "<PERSON><PERSON>"]}