import {
  Graph,
  View,
  loader_exports,
  main_exports,
  main_exports3 as main_exports2
} from "./chunk-WWMWDODG.js";
import "./chunk-PZ5AY32C.js";

// ../../node_modules/.pnpm/@antv+x6-plugin-minimap@2.0.7_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-minimap/es/style/raw.js
var content = `.x6-widget-minimap {
  position: relative;
  display: table-cell;
  box-sizing: border-box;
  overflow: hidden;
  text-align: center;
  vertical-align: middle;
  background-color: #fff;
  user-select: none;
}
.x6-widget-minimap .x6-graph {
  display: inline-block;
  box-shadow: 0 0 4px 0 #eee;
  cursor: pointer;
}
.x6-widget-minimap .x6-graph > svg {
  pointer-events: none;
  shape-rendering: optimizespeed;
}
.x6-widget-minimap .x6-graph .x6-node * {
  /* stylelint-disable-next-line */
  vector-effect: initial;
}
.x6-widget-minimap-viewport {
  position: absolute;
  box-sizing: content-box !important;
  margin: -2px 0 0 -2px;
  border: 2px solid #31d0c6;
  cursor: move;
}
.x6-widget-minimap-viewport-zoom {
  position: absolute;
  right: 0;
  bottom: 0;
  box-sizing: border-box;
  width: 12px;
  height: 12px;
  margin: 0 -6px -6px 0;
  background-color: #fff;
  border: 2px solid #31d0c6;
  border-radius: 50%;
  cursor: nwse-resize;
}
`;

// ../../node_modules/.pnpm/@antv+x6-plugin-minimap@2.0.7_@antv+x6@2.18.1/node_modules/@antv/x6-plugin-minimap/es/index.js
var __decorate = function(decorators, target, key, desc) {
  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
  if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
  return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var MiniMap = class extends View {
  get scroller() {
    return this.graph.getPlugin("scroller");
  }
  get graphContainer() {
    if (this.scroller) {
      return this.scroller.container;
    }
    return this.graph.container;
  }
  constructor(options) {
    super();
    this.name = "minimap";
    this.options = Object.assign(Object.assign({}, Util.defaultOptions), options);
    loader_exports.ensure(this.name, content);
  }
  init(graph) {
    this.graph = graph;
    this.updateViewport = main_exports.debounce(this.updateViewport.bind(this), 0);
    this.container = document.createElement("div");
    main_exports2.addClass(this.container, this.prefixClassName(ClassName.root));
    const graphContainer = document.createElement("div");
    this.container.appendChild(graphContainer);
    this.viewport = document.createElement("div");
    main_exports2.addClass(this.viewport, this.prefixClassName(ClassName.viewport));
    if (this.options.scalable) {
      this.zoomHandle = document.createElement("div");
      main_exports2.addClass(this.zoomHandle, this.prefixClassName(ClassName.zoom));
      main_exports2.appendTo(this.zoomHandle, this.viewport);
    }
    main_exports2.append(this.container, this.viewport);
    main_exports2.css(this.container, {
      width: this.options.width,
      height: this.options.height,
      padding: this.options.padding
    });
    if (this.options.container) {
      this.options.container.appendChild(this.container);
    }
    this.sourceGraph = this.graph;
    const targetGraphOptions = Object.assign(Object.assign({}, this.options.graphOptions), { container: graphContainer, model: this.sourceGraph.model, interacting: false, grid: false, background: false, embedding: false });
    this.targetGraph = this.options.createGraph ? this.options.createGraph(targetGraphOptions) : new Graph(targetGraphOptions);
    this.updatePaper(this.sourceGraph.options.width, this.sourceGraph.options.height);
    this.startListening();
  }
  startListening() {
    if (this.scroller) {
      main_exports2.Event.on(this.graphContainer, `scroll${this.getEventNamespace()}`, this.updateViewport);
    } else {
      this.sourceGraph.on("translate", this.onTransform, this);
      this.sourceGraph.on("scale", this.onTransform, this);
      this.sourceGraph.on("model:updated", this.onModelUpdated, this);
    }
    this.sourceGraph.on("resize", this.updatePaper, this);
    this.delegateEvents({
      mousedown: "startAction",
      touchstart: "startAction",
      [`mousedown .${this.prefixClassName("graph")}`]: "scrollTo",
      [`touchstart .${this.prefixClassName("graph")}`]: "scrollTo"
    });
  }
  stopListening() {
    if (this.scroller) {
      main_exports2.Event.off(this.graphContainer, this.getEventNamespace());
    } else {
      this.sourceGraph.off("translate", this.onTransform, this);
      this.sourceGraph.off("scale", this.onTransform, this);
      this.sourceGraph.off("model:updated", this.onModelUpdated, this);
    }
    this.sourceGraph.off("resize", this.updatePaper, this);
    this.undelegateEvents();
  }
  onRemove() {
    this.stopListening();
    this.targetGraph.dispose(false);
  }
  onTransform(options) {
    if (options.ui || this.targetGraphTransforming || !this.scroller) {
      this.updateViewport();
    }
  }
  onModelUpdated() {
    this.targetGraph.zoomToFit();
  }
  updatePaper(w, h) {
    let width;
    let height;
    if (typeof w === "object") {
      width = w.width;
      height = w.height;
    } else {
      width = w;
      height = h;
    }
    const origin = this.sourceGraph.options;
    const scale = this.sourceGraph.transform.getScale();
    const maxWidth = this.options.width - 2 * this.options.padding;
    const maxHeight = this.options.height - 2 * this.options.padding;
    width /= scale.sx;
    height /= scale.sy;
    this.ratio = Math.min(maxWidth / width, maxHeight / height);
    const ratio = this.ratio;
    const x = origin.x * ratio / scale.sx;
    const y = origin.y * ratio / scale.sy;
    width *= ratio;
    height *= ratio;
    this.targetGraph.resize(width, height);
    this.targetGraph.translate(x, y);
    if (this.scroller) {
      this.targetGraph.scale(ratio, ratio);
    } else {
      this.targetGraph.zoomToFit();
    }
    this.updateViewport();
    return this;
  }
  updateViewport() {
    const sourceGraphScale = this.sourceGraph.transform.getScale();
    const targetGraphScale = this.targetGraph.transform.getScale();
    let origin = null;
    if (this.scroller) {
      origin = this.scroller.clientToLocalPoint(0, 0);
    } else {
      origin = this.graph.graphToLocal(0, 0);
    }
    const position = main_exports2.position(this.targetGraph.container);
    const translation = this.targetGraph.translate();
    translation.ty = translation.ty || 0;
    this.geometry = {
      top: position.top + origin.y * targetGraphScale.sy + translation.ty,
      left: position.left + origin.x * targetGraphScale.sx + translation.tx,
      width: this.graphContainer.clientWidth * targetGraphScale.sx / sourceGraphScale.sx,
      height: this.graphContainer.clientHeight * targetGraphScale.sy / sourceGraphScale.sy
    };
    main_exports2.css(this.viewport, this.geometry);
  }
  startAction(evt) {
    const e = this.normalizeEvent(evt);
    const action = e.target === this.zoomHandle ? "zooming" : "panning";
    const { tx, ty } = this.sourceGraph.translate();
    const eventData = {
      action,
      clientX: e.clientX,
      clientY: e.clientY,
      scrollLeft: this.graphContainer.scrollLeft,
      scrollTop: this.graphContainer.scrollTop,
      zoom: this.sourceGraph.zoom(),
      scale: this.sourceGraph.transform.getScale(),
      geometry: this.geometry,
      translateX: tx,
      translateY: ty
    };
    this.targetGraphTransforming = true;
    this.delegateDocumentEvents(Util.documentEvents, eventData);
  }
  doAction(evt) {
    const e = this.normalizeEvent(evt);
    const clientX = e.clientX;
    const clientY = e.clientY;
    const data = e.data;
    switch (data.action) {
      case "panning": {
        const scale = this.sourceGraph.transform.getScale();
        const rx = (clientX - data.clientX) * scale.sx;
        const ry = (clientY - data.clientY) * scale.sy;
        if (this.scroller) {
          this.graphContainer.scrollLeft = data.scrollLeft + rx / this.ratio;
          this.graphContainer.scrollTop = data.scrollTop + ry / this.ratio;
        } else {
          this.sourceGraph.translate(data.translateX - rx / this.ratio, data.translateY - ry / this.ratio);
        }
        break;
      }
      case "zooming": {
        const startScale = data.scale;
        const startGeometry = data.geometry;
        const delta = 1 + (data.clientX - clientX) / startGeometry.width / startScale.sx;
        if (data.frameId) {
          cancelAnimationFrame(data.frameId);
        }
        data.frameId = requestAnimationFrame(() => {
          this.sourceGraph.zoom(delta * data.zoom, {
            absolute: true,
            minScale: this.options.minScale,
            maxScale: this.options.maxScale
          });
        });
        break;
      }
      default:
        break;
    }
  }
  stopAction() {
    this.undelegateDocumentEvents();
    this.targetGraphTransforming = false;
  }
  scrollTo(evt) {
    const e = this.normalizeEvent(evt);
    let x;
    let y;
    const ts = this.targetGraph.translate();
    ts.ty = ts.ty || 0;
    if (e.offsetX == null) {
      const offset = main_exports2.offset(this.targetGraph.container);
      x = e.pageX - offset.left;
      y = e.pageY - offset.top;
    } else {
      x = e.offsetX;
      y = e.offsetY;
    }
    const cx = (x - ts.tx) / this.ratio;
    const cy = (y - ts.ty) / this.ratio;
    this.sourceGraph.centerPoint(cx, cy);
  }
  dispose() {
    this.remove();
    loader_exports.clean(this.name);
  }
};
__decorate([
  View.dispose()
], MiniMap.prototype, "dispose", null);
var ClassName;
(function(ClassName2) {
  ClassName2.root = "widget-minimap";
  ClassName2.viewport = `${ClassName2.root}-viewport`;
  ClassName2.zoom = `${ClassName2.viewport}-zoom`;
})(ClassName || (ClassName = {}));
var Util;
(function(Util2) {
  Util2.defaultOptions = {
    width: 300,
    height: 200,
    padding: 10,
    scalable: true,
    minScale: 0.01,
    maxScale: 16,
    graphOptions: {},
    createGraph: (options) => new Graph(options)
  };
  Util2.documentEvents = {
    mousemove: "doAction",
    touchmove: "doAction",
    mouseup: "stopAction",
    touchend: "stopAction"
  };
})(Util || (Util = {}));
export {
  MiniMap
};
//# sourceMappingURL=@antv_x6-plugin-minimap.js.map
