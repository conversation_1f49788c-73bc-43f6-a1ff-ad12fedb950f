{"version": 3, "sources": ["../../../../../node_modules/.pnpm/@antv+x6-vue-shape@2.1.2_@antv+x6@2.18.1_vue@3.5.16_typescript@5.8.3_/node_modules/@antv/x6-vue-shape/src/node.ts", "../../../../../node_modules/.pnpm/vue-demi@0.14.10_vue@3.5.16_typescript@5.8.3_/node_modules/vue-demi/lib/index.mjs", "../../../../../node_modules/.pnpm/@antv+x6-vue-shape@2.1.2_@antv+x6@2.18.1_vue@3.5.16_typescript@5.8.3_/node_modules/@antv/x6-vue-shape/src/registry.ts", "../../../../../node_modules/.pnpm/@antv+x6-vue-shape@2.1.2_@antv+x6@2.18.1_vue@3.5.16_typescript@5.8.3_/node_modules/@antv/x6-vue-shape/src/teleport.ts", "../../../../../node_modules/.pnpm/@antv+x6-vue-shape@2.1.2_@antv+x6@2.18.1_vue@3.5.16_typescript@5.8.3_/node_modules/@antv/x6-vue-shape/src/view.ts"], "sourcesContent": ["import { ObjectExt, Markup, Node } from '@antv/x6'\n\nexport class VueShape<\n  Properties extends VueShape.Properties = VueShape.Properties,\n> extends Node<Properties> {}\n\nexport namespace VueShape {\n  export type Primer =\n    | 'rect'\n    | 'circle'\n    | 'path'\n    | 'ellipse'\n    | 'polygon'\n    | 'polyline'\n\n  export interface Properties extends Node.Properties {\n    primer?: Primer\n  }\n}\n\nexport namespace VueShape {\n  function getMarkup(primer?: Primer) {\n    const markup: Markup.JSONMarkup[] = []\n    const content = Markup.getForeignObjectMarkup()\n\n    if (primer) {\n      markup.push(\n        ...[\n          {\n            tagName: primer,\n            selector: 'body',\n          },\n          content,\n        ],\n      )\n    } else {\n      markup.push(content)\n    }\n\n    return markup\n  }\n\n  VueShape.config<Properties>({\n    view: 'vue-shape-view',\n    markup: getMarkup(),\n    attrs: {\n      body: {\n        fill: 'none',\n        stroke: 'none',\n        refWidth: '100%',\n        refHeight: '100%',\n      },\n      fo: {\n        refWidth: '100%',\n        refHeight: '100%',\n      },\n    },\n    propHooks(metadata: Properties) {\n      if (metadata.markup == null) {\n        const primer = metadata.primer\n        if (primer) {\n          metadata.markup = getMarkup(primer)\n\n          let attrs = {}\n          switch (primer) {\n            case 'circle':\n              attrs = {\n                refCx: '50%',\n                refCy: '50%',\n                refR: '50%',\n              }\n              break\n            case 'ellipse':\n              attrs = {\n                refCx: '50%',\n                refCy: '50%',\n                refRx: '50%',\n                refRy: '50%',\n              }\n              break\n            default:\n              break\n          }\n          metadata.attrs = ObjectExt.merge(\n            {},\n            {\n              body: {\n                refWidth: null,\n                refHeight: null,\n                ...attrs,\n              },\n            },\n            metadata.attrs || {},\n          )\n        }\n      }\n      return metadata\n    },\n  })\n\n  Node.registry.register('vue-shape', VueShape, true)\n}\n", "import * as Vue from 'vue'\n\nvar isVue2 = false\nvar isVue3 = true\nvar Vue2 = undefined\n\nfunction install() {}\n\nexport function set(target, key, val) {\n  if (Array.isArray(target)) {\n    target.length = Math.max(target.length, key)\n    target.splice(key, 1, val)\n    return val\n  }\n  target[key] = val\n  return val\n}\n\nexport function del(target, key) {\n  if (Array.isArray(target)) {\n    target.splice(key, 1)\n    return\n  }\n  delete target[key]\n}\n\nexport * from 'vue'\nexport {\n  Vue,\n  Vue2,\n  isVue2,\n  isVue3,\n  install,\n}\n", "import { Graph, Node } from '@antv/x6'\n\nexport type VueShapeConfig = Node.Properties & {\n  shape: string\n  component: any\n  inherit?: string\n}\n\nexport const shapeMaps: Record<\n  string,\n  {\n    component: any\n  }\n> = {}\n\nexport function register(config: VueShapeConfig) {\n  const { shape, component, inherit, ...others } = config\n  if (!shape) {\n    throw new Error('should specify shape in config')\n  }\n  shapeMaps[shape] = {\n    component,\n  }\n\n  Graph.registerNode(\n    shape,\n    {\n      inherit: inherit || 'vue-shape',\n      ...others,\n    },\n    true,\n  )\n}\n", "import { defineComponent, h, reactive, isVue3, Teleport, markRaw, Fragment } from 'vue-demi'\nimport { Graph } from '@antv/x6'\nimport { VueShape } from './node'\n\nlet active = false\nconst items = reactive<{ [key: string]: any }>({})\n\nexport function connect(\n  id: string,\n  component: any,\n  container: HTMLDivElement,\n  node: VueShape,\n  graph: Graph,\n) {\n  if (active) {\n    items[id] = markRaw(\n      defineComponent({\n        render: () => h(Teleport, { to: container } as any, [h(component, { node, graph })]),\n        provide: () => ({\n          getNode: () => node,\n          getGraph: () => graph,\n        }),\n      }),\n    )\n  }\n}\n\nexport function disconnect(id: string) {\n  if (active) {\n    delete items[id]\n  }\n}\n\nexport function isActive() {\n  return active\n}\n\nexport function getTeleport(): any {\n  if (!isVue3) {\n    throw new Error('teleport is only available in Vue3')\n  }\n  active = true\n\n  return defineComponent({\n    setup() {\n      return () =>\n        h(\n          Fragment,\n          {},\n          Object.keys(items).map((id) => h(items[id])),\n        )\n    },\n  })\n}\n", "import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from '@antv/x6'\nimport { isVue2, isVue3, createApp, h, Vue2 } from 'vue-demi'\nimport { VueShape } from './node'\nimport { shapeMaps } from './registry'\nimport { isActive, connect, disconnect } from './teleport'\n\nexport class VueShapeView extends NodeView<VueShape> {\n  private vm: any\n\n  getComponentContainer() {\n    return this.selectors && (this.selectors.foContent as HTMLDivElement)\n  }\n\n  confirmUpdate(flag: number) {\n    const ret = super.confirmUpdate(flag)\n    return this.handleAction(ret, VueShapeView.action, () => {\n      this.renderVueComponent()\n    })\n  }\n\n  protected targetId() {\n    return `${this.graph.view.cid}:${this.cell.id}`\n  }\n\n  protected renderVueComponent() {\n    this.unmountVueComponent()\n    const root = this.getComponentContainer()\n    const node = this.cell\n    const graph = this.graph\n\n    if (root) {\n      const { component } = shapeMaps[node.shape]\n      if (component) {\n        if (isVue2) {\n          const Vue = Vue2 as any\n          this.vm = new Vue({\n            el: root,\n            render(h: any) {\n              return h(component, { node, graph })\n            },\n            provide() {\n              return {\n                getNode: () => node,\n                getGraph: () => graph,\n              }\n            },\n          })\n        } else if (isVue3) {\n          if (isActive()) {\n            connect(this.targetId(), component, root, node, graph)\n          } else {\n            this.vm = createApp({\n              render() {\n                return h(component, { node, graph })\n              },\n              provide() {\n                return {\n                  getNode: () => node,\n                  getGraph: () => graph,\n                }\n              },\n            })\n            this.vm.mount(root)\n          }\n        }\n      }\n    }\n  }\n\n  protected unmountVueComponent() {\n    const root = this.getComponentContainer()\n    if (this.vm) {\n      isVue2 && this.vm.$destroy()\n      isVue3 && this.vm.unmount()\n      this.vm = null\n    }\n    if (root) {\n      root.innerHTML = ''\n    }\n    return root\n  }\n\n  onMouseDown(e: Dom.MouseDownEvent, x: number, y: number) {\n    const target = e.target as Element\n    const tagName = target.tagName.toLowerCase()\n    if (tagName === 'input') {\n      const type = target.getAttribute('type')\n      if (\n        type == null ||\n        [\n          'text',\n          'password',\n          'number',\n          'email',\n          'search',\n          'tel',\n          'url',\n        ].includes(type)\n      ) {\n        return\n      }\n    }\n\n    super.onMouseDown(e, x, y)\n  }\n\n  unmount() {\n    if (isActive()) {\n      disconnect(this.targetId())\n    }\n    this.unmountVueComponent()\n    super.unmount()\n    return this\n  }\n}\n\nexport namespace VueShapeView {\n  export const action = 'vue' as any\n\n  VueShapeView.config({\n    bootstrap: [action],\n    actions: {\n      component: action,\n    },\n  })\n\n  NodeView.registry.register('vue-shape-view', VueShapeView, true)\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAEM,IAAO,WAAP,cAEI,KAAgB;;CAgB1B,SAAiBA,WAAQ;AACvB,WAAS,UAAU,QAAe;AAChC,UAAM,SAA8B,CAAA;AACpC,UAAM,UAAU,OAAO,uBAAsB;AAE7C,QAAI,QAAQ;AACV,aAAO,KACL,GAAG;QACD;UACE,SAAS;UACT,UAAU;;QAEZ;OACD;WAEE;AACL,aAAO,KAAK,OAAO;;AAGrB,WAAO;EACT;AAEA,EAAAA,UAAS,OAAmB;IAC1B,MAAM;IACN,QAAQ,UAAS;IACjB,OAAO;MACL,MAAM;QACJ,MAAM;QACN,QAAQ;QACR,UAAU;QACV,WAAW;;MAEb,IAAI;QACF,UAAU;QACV,WAAW;;;IAGf,UAAU,UAAoB;AAC5B,UAAI,SAAS,UAAU,MAAM;AAC3B,cAAM,SAAS,SAAS;AACxB,YAAI,QAAQ;AACV,mBAAS,SAAS,UAAU,MAAM;AAElC,cAAI,QAAQ,CAAA;AACZ,kBAAQ,QAAQ;YACd,KAAK;AACH,sBAAQ;gBACN,OAAO;gBACP,OAAO;gBACP,MAAM;;AAER;YACF,KAAK;AACH,sBAAQ;gBACN,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,OAAO;;AAET;YACF;AACE;;AAEJ,mBAAS,QAAQ,eAAU,MACzB,CAAA,GACA;YACE,MAAI,OAAA,OAAA,EACF,UAAU,MACV,WAAW,KAAI,GACZ,KAAK;aAGZ,SAAS,SAAS,CAAA,CAAE;;;AAI1B,aAAO;IACT;GACD;AAED,OAAK,SAAS,SAAS,aAAaA,WAAU,IAAI;AACpD,GAjFiB,aAAA,WAAQ,CAAA,EAAA;;;AClBzB,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,OAAO;;;;;;;;;;;;;;ACIJ,IAAM,YAKT,CAAA;AAEE,SAAU,SAAS,QAAsB;AAC7C,QAAM,EAAE,OAAO,WAAW,QAAO,IAAgB,QAAX,SAAM,OAAK,QAA3C,CAAA,SAAA,aAAA,SAAA,CAAwC;AAC9C,MAAI,CAAC,OAAO;AACV,UAAM,IAAI,MAAM,gCAAgC;;AAElD,YAAU,KAAK,IAAI;IACjB;;AAGF,QAAM,aACJ,OAAK,OAAA,OAAA,EAEH,SAAS,WAAW,YAAW,GAC5B,MAAM,GAEX,IAAI;AAER;;;AC5BA,IAAI,SAAS;AACb,IAAM,QAAQ,SAAiC,CAAA,CAAE;AAE3C,SAAU,QACd,IACA,WACA,WACA,MACA,OAAY;AAEZ,MAAI,QAAQ;AACV,UAAM,EAAE,IAAI,QACV,gBAAgB;MACd,QAAQ,MAAM,EAAE,UAAU,EAAE,IAAI,UAAS,GAAW,CAAC,EAAE,WAAW,EAAE,MAAM,MAAK,CAAE,CAAC,CAAC;MACnF,SAAS,OAAO;QACd,SAAS,MAAM;QACf,UAAU,MAAM;;KAEnB,CAAC;;AAGR;AAEM,SAAU,WAAW,IAAU;AACnC,MAAI,QAAQ;AACV,WAAO,MAAM,EAAE;;AAEnB;AAEM,SAAU,WAAQ;AACtB,SAAO;AACT;AAEM,SAAU,cAAW;AACzB,MAAI,CAAC,QAAQ;AACX,UAAM,IAAI,MAAM,oCAAoC;;AAEtD,WAAS;AAET,SAAO,gBAAgB;IACrB,QAAK;AACH,aAAO,MACL,EACE,UACA,CAAA,GACA,OAAO,KAAK,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IAElD;GACD;AACH;;;AC/CM,IAAO,eAAP,MAAO,sBAAqB,SAAkB;EAGlD,wBAAqB;AACnB,WAAO,KAAK,aAAc,KAAK,UAAU;EAC3C;EAEA,cAAc,MAAY;AACxB,UAAM,MAAM,MAAM,cAAc,IAAI;AACpC,WAAO,KAAK,aAAa,KAAK,cAAa,QAAQ,MAAK;AACtD,WAAK,mBAAkB;IACzB,CAAC;EACH;EAEU,WAAQ;AAChB,WAAO,GAAG,KAAK,MAAM,KAAK,GAAG,IAAI,KAAK,KAAK,EAAE;EAC/C;EAEU,qBAAkB;AAC1B,SAAK,oBAAmB;AACxB,UAAM,OAAO,KAAK,sBAAqB;AACvC,UAAM,OAAO,KAAK;AAClB,UAAM,QAAQ,KAAK;AAEnB,QAAI,MAAM;AACR,YAAM,EAAE,UAAS,IAAK,UAAU,KAAK,KAAK;AAC1C,UAAI,WAAW;AACb,YAAI,QAAQ;AACV,gBAAM,MAAM;AACZ,eAAK,KAAK,IAAI,IAAI;YAChB,IAAI;YACJ,OAAOC,IAAM;AACX,qBAAOA,GAAE,WAAW,EAAE,MAAM,MAAK,CAAE;YACrC;YACA,UAAO;AACL,qBAAO;gBACL,SAAS,MAAM;gBACf,UAAU,MAAM;;YAEpB;WACD;mBACQ,QAAQ;AACjB,cAAI,SAAQ,GAAI;AACd,oBAAQ,KAAK,SAAQ,GAAI,WAAW,MAAM,MAAM,KAAK;iBAChD;AACL,iBAAK,KAAK,UAAU;cAClB,SAAM;AACJ,uBAAO,EAAE,WAAW,EAAE,MAAM,MAAK,CAAE;cACrC;cACA,UAAO;AACL,uBAAO;kBACL,SAAS,MAAM;kBACf,UAAU,MAAM;;cAEpB;aACD;AACD,iBAAK,GAAG,MAAM,IAAI;;;;;EAK5B;EAEU,sBAAmB;AAC3B,UAAM,OAAO,KAAK,sBAAqB;AACvC,QAAI,KAAK,IAAI;AACX,gBAAU,KAAK,GAAG,SAAQ;AAC1B,gBAAU,KAAK,GAAG,QAAO;AACzB,WAAK,KAAK;;AAEZ,QAAI,MAAM;AACR,WAAK,YAAY;;AAEnB,WAAO;EACT;EAEA,YAAY,GAAuB,GAAW,GAAS;AACrD,UAAM,SAAS,EAAE;AACjB,UAAM,UAAU,OAAO,QAAQ,YAAW;AAC1C,QAAI,YAAY,SAAS;AACvB,YAAM,OAAO,OAAO,aAAa,MAAM;AACvC,UACE,QAAQ,QACR;QACE;QACA;QACA;QACA;QACA;QACA;QACA;QACA,SAAS,IAAI,GACf;AACA;;;AAIJ,UAAM,YAAY,GAAG,GAAG,CAAC;EAC3B;EAEA,UAAO;AACL,QAAI,SAAQ,GAAI;AACd,iBAAW,KAAK,SAAQ,CAAE;;AAE5B,SAAK,oBAAmB;AACxB,UAAM,QAAO;AACb,WAAO;EACT;;CAGF,SAAiBC,eAAY;AACd,EAAAA,cAAA,SAAS;AAEtB,EAAAA,cAAa,OAAO;IAClB,WAAW,CAACA,cAAA,MAAM;IAClB,SAAS;MACP,WAAWA,cAAA;;GAEd;AAED,WAAS,SAAS,SAAS,kBAAkBA,eAAc,IAAI;AACjE,GAXiB,iBAAA,eAAY,CAAA,EAAA;", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "h", "<PERSON><PERSON><PERSON>hape<PERSON>iew"]}