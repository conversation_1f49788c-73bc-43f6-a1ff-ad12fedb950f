#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Project/NanAo/Dev/nanao-three-next/node_modules/.pnpm/vite@5.4.19_@types+node@22.15.32_sass@1.89.2/node_modules/vite/bin/node_modules:/Users/<USER>/Project/NanAo/Dev/nanao-three-next/node_modules/.pnpm/vite@5.4.19_@types+node@22.15.32_sass@1.89.2/node_modules/vite/node_modules:/Users/<USER>/Project/NanAo/Dev/nanao-three-next/node_modules/.pnpm/vite@5.4.19_@types+node@22.15.32_sass@1.89.2/node_modules:/Users/<USER>/Project/NanAo/Dev/nanao-three-next/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Project/NanAo/Dev/nanao-three-next/node_modules/.pnpm/vite@5.4.19_@types+node@22.15.32_sass@1.89.2/node_modules/vite/bin/node_modules:/Users/<USER>/Project/NanAo/Dev/nanao-three-next/node_modules/.pnpm/vite@5.4.19_@types+node@22.15.32_sass@1.89.2/node_modules/vite/node_modules:/Users/<USER>/Project/NanAo/Dev/nanao-three-next/node_modules/.pnpm/vite@5.4.19_@types+node@22.15.32_sass@1.89.2/node_modules:/Users/<USER>/Project/NanAo/Dev/nanao-three-next/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../vite/bin/vite.js" "$@"
fi
