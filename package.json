{"name": "ddd-flow", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "lint:check": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --ignore-path .gitignore", "format": "prettier --write src/", "format:check": "prettier --check src/", "type-check": "vue-tsc --noEmit", "build:analyze": "vite build --mode analyze", "prepare": "husky install", "pre-commit": "lint-staged", "ci": "npm run lint:check && npm run format:check && npm run type-check && npm run test:run"}, "dependencies": {"@antv/x6": "^2.18.1", "@antv/x6-plugin-minimap": "^2.0.7", "@antv/x6-vue-shape": "^2.1.2", "vue": "^3.3.11"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "@vue/test-utils": "^2.4.3", "@vitest/ui": "^1.1.0", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/parser": "^6.18.1", "eslint-plugin-vue": "^9.19.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-unused-imports": "^3.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/eslint-config-prettier": "^9.0.0", "happy-dom": "^12.10.3", "jsdom": "^23.2.0", "prettier": "^3.1.1", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.1.0", "vue-tsc": "^1.8.25", "@vitest/coverage-v8": "^1.1.0", "lint-staged": "^15.2.0", "husky": "^8.0.3"}}