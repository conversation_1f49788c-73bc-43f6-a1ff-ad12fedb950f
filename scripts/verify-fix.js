#!/usr/bin/env node

/**
 * 验证节点连接系统修复的脚本
 * 检查端口选择器是否唯一，避免运行时错误
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 验证节点连接系统修复...\n');

// 读取GraphCanvas.vue文件
const graphCanvasPath = path.join(__dirname, '../src/components/editor/GraphCanvas.vue');

if (!fs.existsSync(graphCanvasPath)) {
  console.error('❌ 找不到GraphCanvas.vue文件');
  process.exit(1);
}

const content = fs.readFileSync(graphCanvasPath, 'utf8');

// 检查端口选择器唯一性
console.log('1. 检查端口选择器唯一性...');

const selectorPattern = /selector:\s*['"`]([^'"`]+)['"`]/g;
const selectors = [];
let match;

while ((match = selectorPattern.exec(content)) !== null) {
  selectors.push(match[1]);
}

const uniqueSelectors = [...new Set(selectors)];
const hasDuplicates = selectors.length !== uniqueSelectors.length;

if (hasDuplicates) {
  console.error('❌ 发现重复的选择器名称！');
  
  // 找出重复的选择器
  const duplicates = selectors.filter((item, index) => selectors.indexOf(item) !== index);
  const uniqueDuplicates = [...new Set(duplicates)];
  
  console.error('重复的选择器:', uniqueDuplicates.join(', '));
  process.exit(1);
} else {
  console.log('✅ 所有端口选择器都是唯一的');
  console.log(`   找到 ${selectors.length} 个选择器: ${uniqueSelectors.join(', ')}`);
}

// 检查是否移除了visibility: 'hidden'
console.log('\n2. 检查端口可见性设置...');

const hiddenVisibilityPattern = /visibility:\s*['"`]hidden['"`]/g;
const hiddenMatches = content.match(hiddenVisibilityPattern);

if (hiddenMatches && hiddenMatches.length > 0) {
  console.error('❌ 仍然存在 visibility: "hidden" 设置！');
  console.error(`   找到 ${hiddenMatches.length} 处隐藏设置`);
  process.exit(1);
} else {
  console.log('✅ 没有发现端口隐藏设置');
}

// 检查端口组配置
console.log('\n3. 检查端口组配置...');

const expectedPortGroups = [
  'exec-in',
  'exec-out', 
  'data-in',
  'data-out',
  'exec-out-true',
  'exec-out-false'
];

const portGroupPattern = /'([^']+)':\s*{[^}]*position:/g;
const foundPortGroups = [];

while ((match = portGroupPattern.exec(content)) !== null) {
  foundPortGroups.push(match[1]);
}

const missingGroups = expectedPortGroups.filter(group => !foundPortGroups.includes(group));
const extraGroups = foundPortGroups.filter(group => !expectedPortGroups.includes(group));

if (missingGroups.length > 0) {
  console.warn('⚠️  缺少端口组:', missingGroups.join(', '));
}

if (extraGroups.length > 0) {
  console.log('ℹ️  额外的端口组:', extraGroups.join(', '));
}

console.log('✅ 端口组配置检查完成');
console.log(`   找到端口组: ${foundPortGroups.join(', ')}`);

// 检查悬停效果函数
console.log('\n4. 检查悬停效果函数...');

const hoverFunctionPattern = /setupPortHoverEffects\(\)/;
const hasHoverFunction = hoverFunctionPattern.test(content);

if (!hasHoverFunction) {
  console.error('❌ 找不到setupPortHoverEffects函数调用');
  process.exit(1);
} else {
  console.log('✅ 悬停效果函数配置正确');
}

// 检查连接验证函数
console.log('\n5. 检查连接验证配置...');

const validateConnectionPattern = /validateConnection.*{[\s\S]*?}/;
const hasValidation = validateConnectionPattern.test(content);

if (!hasValidation) {
  console.warn('⚠️  找不到连接验证配置');
} else {
  console.log('✅ 连接验证配置存在');
}

// 检查节点注册
console.log('\n6. 检查节点注册...');

const nodeRegistrationPattern = /Graph\.registerNode\(\s*['"`]([^'"`]+)['"`]/g;
const registeredNodes = [];

while ((match = nodeRegistrationPattern.exec(content)) !== null) {
  registeredNodes.push(match[1]);
}

const expectedNodes = ['blueprint-node', 'logic-branch-node'];
const missingNodes = expectedNodes.filter(node => !registeredNodes.includes(node));

if (missingNodes.length > 0) {
  console.error('❌ 缺少节点注册:', missingNodes.join(', '));
  process.exit(1);
} else {
  console.log('✅ 所有必需的节点都已注册');
  console.log(`   注册的节点: ${registeredNodes.join(', ')}`);
}

console.log('\n🎉 所有检查都通过了！');
console.log('\n📋 修复总结:');
console.log('   ✅ 端口选择器唯一性 - 已修复');
console.log('   ✅ 端口可见性 - 已修复');
console.log('   ✅ 端口组配置 - 完整');
console.log('   ✅ 悬停效果 - 已配置');
console.log('   ✅ 节点注册 - 完整');

console.log('\n🚀 现在可以安全地运行应用程序了！');
console.log('   运行: npm run dev');
console.log('   访问: http://localhost:3001/');

process.exit(0);
