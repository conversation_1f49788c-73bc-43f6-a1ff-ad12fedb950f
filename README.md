# DDD-Flow 可视化场景编辑器

基于领域驱动设计（DDD）原则的可视化场景编辑器，提供类似Unreal Engine蓝图编辑器的节点连接体验。

## 🚀 最新更新

### 节点连接系统重大改进 ✨

我们完全重构了节点连接系统，实现了类似Unreal Engine蓝图编辑器的视觉效果和交互体验：

#### ✅ 修复的问题
- **端口可见性**：修复了所有节点端口不可见的问题
- **连接交互**：实现了直观的拖拽连接操作
- **视觉反馈**：添加了端口悬停高亮效果

#### 🎯 新增特性
- **智能端口系统**：
  - 执行流端口（白色圆形）- 控制节点执行顺序
  - 数据流端口（蓝色圆形）- 传递数据值
  - 条件分支端口（绿色/红色）- True/False输出
- **连接验证**：
  - 执行流只能连接到执行流
  - 数据流只能连接到数据流
  - 防止无效连接（如输出到输出）
- **视觉效果**：
  - 贝塞尔曲线连接线
  - 端口悬停放大和发光效果
  - 连接线类型颜色区分
  - 平滑的动画过渡

## 功能特性

- 🎨 **可视化编辑**：直观的拖拽式节点编辑器
- 🔗 **蓝图式连接**：类似Unreal Engine的节点连接系统
- 🎯 **智能端口**：执行流和数据流的可视化区分
- 🔧 **多种节点类型**：支持事件、动作、逻辑、数据等多种节点
- 🎯 **场景管理**：支持多场景创建、切换和管理
- 📝 **配置生成**：自动生成可执行的配置文件
- 💾 **数据持久化**：本地存储场景数据
- 🔍 **搜索过滤**：快速查找和过滤节点
- ⚡ **性能优化**：缓存机制和性能监控
- 🎹 **快捷键支持**：丰富的键盘快捷键
- 📚 **完整文档**：详细的API文档和用户指南

## 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

## 节点连接系统使用指南

### 端口类型

| 端口类型 | 颜色 | 用途 | 连接规则 |
|---------|------|------|----------|
| 执行流输入 | 白色 | 接收执行信号 | 只能从执行流输出连接 |
| 执行流输出 | 白色 | 发送执行信号 | 只能连接到执行流输入 |
| 数据输入 | 蓝色 | 接收数据值 | 只能从数据输出连接 |
| 数据输出 | 蓝色 | 发送数据值 | 只能连接到数据输入 |
| True输出 | 绿色 | 条件为真时执行 | 连接到执行流输入 |
| False输出 | 红色 | 条件为假时执行 | 连接到执行流输入 |

### 操作方法

1. **查看端口**：鼠标悬停在节点边缘查看可用端口
2. **创建连接**：从输出端口拖拽到目标输入端口
3. **端口标签**：悬停端口时显示类型标签
4. **连接验证**：系统自动验证连接的有效性
5. **视觉反馈**：连接线颜色表示不同的数据类型

### 节点类型和端口配置

#### 事件节点
- **端口**：仅有执行流输出
- **用途**：响应用户交互或系统事件
- **示例**：点击事件、悬停事件、生命周期事件

#### 动作节点
- **端口**：执行流输入和输出
- **用途**：执行具体的操作
- **示例**：高亮显示、相机控制、动画播放

#### 逻辑分支节点
- **端口**：执行流输入，True/False输出
- **用途**：根据条件控制执行流向
- **特色**：绿色True端口，红色False端口

#### 数据节点
- **端口**：数据输入/输出，部分有执行流端口
- **用途**：数据处理和传递
- **示例**：数据源、数据转换、数据消费

## 项目结构

```
src/
├── components/
│   ├── editor/
│   │   ├── GraphCanvas.vue      # 核心图形编辑器
│   │   ├── EditorToolbar.vue    # 编辑器工具栏
│   │   └── NodeLibrary.vue      # 节点库
│   ├── common/
│   │   └── SearchFilter.vue     # 搜索过滤组件
│   └── demo/
│       └── NodeConnectionDemo.vue # 连接系统演示
├── services/                    # 业务服务层
├── types/                       # TypeScript 类型定义
└── docs/                        # 文档
```

## 开发指南

### 添加新节点类型

1. 在 `types/index.ts` 中定义节点类型
2. 在 `GraphCanvas.vue` 的 `getNodePorts()` 中配置端口
3. 在 `setNodeStyle()` 中定义节点样式
4. 在 `NodeLibrary.vue` 中添加节点选项

### 自定义端口类型

```typescript
// 在 registerCustomNodes() 中添加新的端口组
'custom-port': {
  position: { name: 'right', args: { y: 30 } },
  attrs: {
    circle: {
      r: 5,
      magnet: true,
      stroke: '#custom-color',
      strokeWidth: 2,
      fill: '#custom-fill',
      'port-group': 'custom-port',
    },
  },
}
```

## 测试

```bash
# 运行单元测试
npm run test

# 运行测试覆盖率
npm run test:coverage

# 运行测试UI
npm run test:ui
```

## 文档

- [API 文档](./docs/api/README.md)
- [开发指南](./docs/development/README.md)
- [用户手册](./docs/user/README.md)

## 技术栈

- **前端框架**：Vue 3 + TypeScript
- **图形库**：AntV X6
- **构建工具**：Vite
- **测试框架**：Vitest + Vue Test Utils
- **代码质量**：ESLint + Prettier
- **样式**：CSS3 + 动画效果

## 贡献

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License

## 更新日志

### v1.2.0 (2024-01-XX)
- 🎯 **重大更新**：完全重构节点连接系统
- ✨ 实现类似UE蓝图的端口可视化
- 🔗 添加智能连接验证
- 🎨 优化视觉效果和动画
- 📝 完善文档和测试

### v1.1.0
- 添加搜索和过滤功能
- 实现撤销/重做功能
- 优化性能和用户体验

### v1.0.0
- 初始版本发布
- 基本的节点编辑功能
- 场景管理和配置生成
